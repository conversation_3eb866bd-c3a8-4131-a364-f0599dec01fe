package main

import (
	"fmt"
	"log"
	"os"

	"content-service/internal/bootstrap"
	"content-service/pkg/config"
	"content-service/pkg/db"
)

func main() {
	config.LoadConfig()

	database := db.ConnectDB()
	// db.<PERSON><PERSON><PERSON>(database)
	// db.Seed(database)

	port := config.Cfg.App.HttpPort
	appCtx := bootstrap.SetupApp(database, &config.Cfg)

	// Start task worker
	// worker.StartTaskWorker(appCtx.Redis)

	log.Printf("🚀 Starting %s on :%d", config.Cfg.App.AppName, port)

	// Check if certificate files exist for HTTPS
	certFile := config.Cfg.App.CertFile
	keyFile := config.Cfg.App.KeyFile

	if certFile != "" && keyFile != "" {
		// Check if certificate files exist
		if _, err := os.Stat(certFile); err == nil {
			if _, err := os.Stat(keyFile); err == nil {
				log.Printf("🔒 Starting HTTPS server with certificates: %s, %s", certFile, keyFile)
				log.Fatal(appCtx.App.ListenTLS(fmt.Sprintf(":%d", port), certFile, keyFile))
			}
		}
	}

	// Fallback to HTTP if certificates not found
	log.Printf("🌐 Starting HTTP server")
	log.Fatal(appCtx.App.Listen(fmt.Sprintf(":%d", port)))
}
