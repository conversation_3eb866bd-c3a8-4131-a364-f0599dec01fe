#!/bin/bash


if [ -n "$1" ]; then
  service=$1
else
  echo "Select service to run:"
  echo "1) content_service"
  echo "2) system_admin_service"
  echo "3) auction_core_service"
  echo "4) auctioneer_service"
  echo "5) user_service"
  read -p "Enter number: " service
fi

case $service in
  1)
    echo "Running content_service..."
    go run content_service/cmd/main.go
    ;;
  2)
    echo "Running system_admin_service..."
    go run system_admin_service/cmd/main.go
    ;;
  3)
    echo "Running auction_core_service..."
    go run auction_core_service/cmd/main.go
    ;;
  4)
    echo "Running auctioneer_service..."
    go run auctioneer_service/cmd/main.go
    ;;
  5)
    echo "Running user_service..."
    go run user_service/cmd/main.go
    ;;
  *)
    echo "Invalid selection"
    exit 1
    ;;    
esac
