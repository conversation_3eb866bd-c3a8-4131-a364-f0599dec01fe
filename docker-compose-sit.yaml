services:
  content-service:
    image: harbor-npd.auct.local/auctlive-sit/content-service:${IMAGE_TAG}
    build:
      context: .
      dockerfile: Dockerfile.sit
      args:
        ENV_FILE: .env.sit

    volumes:
      - ./cert:/app/cert
    environment:
      APP_ENV: sit
    networks:
      - auction-microservices_auctlive-net

networks:
  auction-microservices_auctlive-net:
    external: true
