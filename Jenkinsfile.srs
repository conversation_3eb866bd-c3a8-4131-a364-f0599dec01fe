pipeline {
  agent {
    label 'agent-auction'
  }

  environment {
    COMPOSE_FILE = "docker-compose-dev.yaml"
    SERVICE_NAME = "content-core-service"
  }

  stages {
    stage('Check Docker') {
      steps {
        sh 'docker version'
        sh 'docker compose version'
      }
    }

    stage('Checkout') {
      steps {
        checkout([$class: 'GitSCM',
          branches: [[name: '*/develop']],
          userRemoteConfigs: [[
            url: 'https://<EMAIL>/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-content',
            credentialsId: 'auction-azure-srs'
          ]]
        ])
      }
    }
    
    stage('Prepare Network') {
      steps {
        sh 'docker network inspect auction-microservices_net >/dev/null 2>&1 || docker network create auction-microservices_net'
      }
    }

    stage('Stop & Remove Old Container') {
      steps {
        sh "docker compose -f $COMPOSE_FILE down || true"
      }
    }

    stage('Clone Common Lib') {
      steps {
        dir('backend-common-lib') {
          git credentialsId: 'auction-azure-srs',
            url: 'https://<EMAIL>/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-common-lib',
            branch: 'main',
            changelog: false,
            poll: false
        }
      }
    }

    stage('Setup Certificates') {
      steps {
        script {
          sh 'mkdir -p cert'
          
          withCredentials([
            file(credentialsId: 'secret-dev', variable: 'DEV_PEM'),
            file(credentialsId: 'secret-dev-key', variable: 'DEV_KEY_PEM'),
            file(credentialsId: 'secret-erp_cert', variable: 'ERP_CERT_PEM'),
            file(credentialsId: 'secret-localhost-key', variable: 'LOCALHOST_KEY_PEM'),
            file(credentialsId: 'secret-localhost', variable: 'LOCALHOST_PEM')
          ]) {
            sh '''
              cp $DEV_PEM cert/dev.pem
              cp $DEV_KEY_PEM cert/dev-key.pem
              cp $ERP_CERT_PEM cert/erp_cert.crt
              cp $LOCALHOST_PEM cert/localhost.pem
              cp $LOCALHOST_KEY_PEM cert/localhost-key.pem
              chmod 644 cert/*.pem cert/*.crt || true
            '''
          }
        }
      }
    }

    stage('Build & Run via Compose') {
      steps {
        sh "docker compose -f $COMPOSE_FILE up -d --build $SERVICE_NAME"
      }
    }

    stage('Wait for Container') {
      steps {
        sh 'sleep 10'
      }
    }

    stage('Copy Certificates to Container') {
      steps {
        script {
          def containerName = "content-core-service"
          def certFiles = [
            "dev.pem",
            "dev-key.pem",
            "localhost.pem",
            "localhost-key.pem",
            "erp_cert.crt"
          ]

          // Check if container is running
          def containerStatus = sh(script: "docker inspect --format='{{.State.Status}}' ${containerName}", returnStdout: true).trim()
          if (containerStatus != "running") {
            error "Container ${containerName} is not running. Status: ${containerStatus}"
          }

          certFiles.each { file ->
            sh "docker cp cert/${file} ${containerName}:/app/cert/${file}"
          }

          sh "docker exec ${containerName} ls -la /app/cert || true"
        }
      }
    }

    stage('Show Container Logs') {
      steps {
        sh "docker logs --tail=20 content-core-service || true"
      }
    }

    stage('Cleanup') {
      steps {
        sh "docker image prune -f || true"
      }
    }
  }

  post {
    failure {
      echo "Build or Deploy failed!"
    }
    success {
      echo "Deployed via docker compose successfully"
    }
  }
}

