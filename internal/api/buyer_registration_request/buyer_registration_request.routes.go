package buyerregistrationrequest

import (
	repository "content-service/internal/repository/buyer_registration_request"
	service "content-service/internal/service/buyer_registration_request"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB) {
	repo := repository.NewBuyerRegistrationRequestRepository(db)
	service := service.NewBuyerRegistrationRequestService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("buyer-registration-request")
	route.Post("/search", h.SearchBuyerRegistrationRequestFilter)
	route.Put("/status/:id", h.UpdateBuyerRegistrationRequestStatus)
}
