package reprintslipreason

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/reprint_slip_reason"
	service "content-service/internal/service/reprint_slip_reason"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewReprintSlipReasonRepository(db)
	service := service.NewReprintSlipReasonService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("reprint-slip-reasons")
	route.Post("/search", h.GetAllReprintSlipReason)
	route.Post("/", h.CreateReprintSlipReason)
	route.Get("/:id", h.GetReprintSlipReasonById)
	route.Put("/:id", h.UpdateReprintSlipReason)
	route.Put("/status/:id", h.UpdateReprintSlipReasonStatus)
	route.Delete("/:id", h.DeleteReprintSlipReasonByID)
}
