package mastercustomer

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_customer"
	service "content-service/internal/service/master_customer"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCustomerRepository(db)
	service := service.NewMasterCustomerService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("customers")
	route.Post("/", h.SearchMasterCustomerFilter)
	route.Put("/status/:id", h.UpdateMasterCustomerStatus)
	route.Post("/sync", h.SyncMasterCustomerFromErp)
}
