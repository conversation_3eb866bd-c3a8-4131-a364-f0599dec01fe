package mastercostrevenue

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_cost_revenue"
	service "content-service/internal/service/master_cost_revenue"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCostRevenueRepository(db)
	service := service.NewMasterCostRevenueService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("cost-revenues")
	route.Post("/", h.SearchMasterCostRevenueFilter)
	route.Put("/status/:id", h.UpdateMasterCostRevenueStatus)
	route.Post("/sync", h.SyncMasterCostRevenueFromErp)

}
