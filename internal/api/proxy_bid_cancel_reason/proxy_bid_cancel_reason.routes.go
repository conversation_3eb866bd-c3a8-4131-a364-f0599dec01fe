package proxybidcancelreason

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/proxy_bid_cancel_reason"
	service "content-service/internal/service/proxy_bid_cancel_reason"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewProxyBidCancelReasonRepository(db)
	service := service.NewProxyBidCancelReasonService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("cancel-reasons")
	route.Post("/search", h.GetProxyBidCancelReason)
	route.Get("/:id", h.GetProxyBidCancelReasonByID)
	route.Post("/", h.CreateProxyBidCancelReason)
	route.Put("/:id", h.UpdateProxyBidCancelReason)
	route.Put("/status/:id", h.UpdateProxyBidCancelReasonStatus)
	route.Delete("/:id", h.DeleteProxyBidCancelReason)
}
