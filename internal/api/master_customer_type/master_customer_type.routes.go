package mastercustomertype

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_customer_type"
	service "content-service/internal/service/master_customer_type"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCustomerTypeRepository(db)
	service := service.NewMasterCustomerTypeService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("customer-types")
	route.Post("/", h.SearchMasterCustomerTypeFilter)
	route.Put("/status/:id", h.UpdateMasterCustomerTypeStatus)
	route.Post("/sync", h.SyncMasterCustomerTypeFromErp)
}
