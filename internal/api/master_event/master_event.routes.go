package masterevent

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_event"
	service "content-service/internal/service/master_event"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterEventRepository(db)
	service := service.NewMasterEventService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("events")
	route.Get("/all", h.GetMasterEventAll)
	route.Post("/", h.SearchMasterEventFilter)
	route.Put("/status/:id", h.UpdateMasterEventStatus)
	route.Post("/sync", h.SyncMasterEventFromErp)

}
