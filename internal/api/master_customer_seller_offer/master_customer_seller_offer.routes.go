package masterassetgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_customer_seller_offer"
	service "content-service/internal/service/master_customer_seller_offer"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCustomerSellerOfferRepository(db)
	service := service.NewMasterCustomerSellerOfferService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("customer-seller-offers")
	route.Post("/", h.SearchMasterCustomerSellerOfferFilter)
	route.Put("/status/:id", h.UpdateMasterCustomerSellerOfferStatus)
	route.Post("/sync", h.SyncMasterCustomerSellerOfferFromErp)
}
