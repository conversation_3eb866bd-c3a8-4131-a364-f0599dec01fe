package masterpaymentmethod

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_payment_method"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.MasterPaymentMethodService
	ErpConfig global.ErpConfig
}

func (h *Handler) SearchMasterPaymentMethodFilter(c *fiber.Ctx) error {
	var req dto.MasterPaymentMethodPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterPaymentMethodFilter(req)
	if err != nil {
		return err
	}

	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateMasterPaymentMethodStatus(c *fiber.Ctx) error {
	var req dto.MasterPaymentMethodUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterPaymentMethodStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) SyncMasterPaymentMethodFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterPaymentMethodFromErp(req.ActionBy, h.ErpConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
