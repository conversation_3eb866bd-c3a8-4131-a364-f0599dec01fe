package feature

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/feature"
	service "content-service/internal/service/feature"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewFeatureRepositoryImpl(db)
	service := service.NewMasterFeatureService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(route fiber.Router, h *Handler) {
	route.Get("/features", h.GetFeatureAll)
}
