package masterholiday

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_holiday"
	service "content-service/internal/service/master_holiday"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterHolidayRepository(db)
	service := service.NewMasterHolidayService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("holidays")
	route.Post("", h.SearchMasterHolidayFilter)
	route.Put("/status/:id", h.UpdateMasterHolidayStatus)
	route.Post("/sync", h.SyncMasterHolidayFromErp)
}
