package userbiddernumbers

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/user_bidder_numbers"
	service "content-service/internal/service/user_bidder_numbers"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewUserBidderNumbersRepository(db)
	service := service.NewUserBidderNumbersService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	r.Get("/:bidderNumber", h.GetUserBidderNumberByBidderNumber)
}
