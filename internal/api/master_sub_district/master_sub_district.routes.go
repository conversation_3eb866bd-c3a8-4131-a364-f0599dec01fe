package mastersubdistrict

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_sub_district"
	service "content-service/internal/service/master_sub_district"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterSubDistrictRepository(db)
	service := service.NewMasterSubDistrictService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("sub-districts")
	route.Post("/", h.SearchMasterSubDistrictFilter)
	route.Put("/status/:id", h.UpdateMasterSubDistrictStatus)
	route.Post("/sync", h.SyncMasterSubDistrictFromErp)
}
