package masterassetgroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_register_type"
	service "content-service/internal/service/master_register_type"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterRegisterTypeRepository(db)
	service := service.NewMasterRegisterTypeService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("register-types")
	route.Post("/", h.SearchMasterRegisterTypeFilter)
	route.Put("/status/:id", h.UpdateMasterRegisterTypeStatus)
	route.Post("/sync", h.SyncMasterRegisterTypeFromErp)
}
