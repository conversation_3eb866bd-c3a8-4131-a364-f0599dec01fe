package masterbank

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_bank"
	service "content-service/internal/service/master_bank"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterBankRepository(db)
	service := service.NewMasterBankService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}
func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("banks")

	route.Post("", h.SearchMasterBankFilter)
	route.Put("/status/:id", h.UpdateMasterBankStatus)
	route.Post("/sync", h.SyncMasterBankFromErp)
}
