package floorspecialremark

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/floor_special_remark"
	service "content-service/internal/service/floor_special_remark"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewFloorSpecialRemarkRepository(db)
	service := service.NewFloorSpecialRemarkService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("floor-special-remarks")
	route.Post("/search", h.GetFloorSpecialRemark)
	route.Get("/:id", h.GetFloorSpecialRemarkByID)
	route.Post("/", h.CreateFloorSpecialRemark)
	route.Put("/:id", h.UpdateFloorSpecialRemark)
	route.Put("/status/:id", h.UpdateFloorSpecialRemarkStatus)
	route.Delete("/:id", h.DeleteFloorSpecialRemark)
}
