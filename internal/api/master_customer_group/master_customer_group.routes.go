package mastercustomergroup

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_customer_group"
	service "content-service/internal/service/master_customer_group"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCustomerGroupRepository(db)
	service := service.NewMasterCustomerGroupService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("customer-groups")
	route.Post("/", h.SearchMasterCustomerGroupFilter)
	route.Put("/status/:id", h.UpdateMasterCustomerGroupStatus)
	route.Post("/sync", h.SyncMasterCustomerGroupFromErp)
	route.Get("/", h.FindMasterCustomerGroupAll)
}
