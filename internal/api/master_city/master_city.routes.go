package mastercity

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/master_city"
	service "content-service/internal/service/master_city"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewMasterCityRepository(db)
	service := service.NewMasterCityService(repo)
	handler := &Handler{Service: service, ErpConfig: config.Erp}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("cities")
	route.Post("/", h.SearchMasterCityFilter)
	route.Put("/status/:id", h.UpdateMasterCityStatus)
	route.Post("/sync", h.SyncMasterCityFromErp)
}
