package paymentduenotification

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/payment_due_notification"
	repositoryExcludedBuyer "content-service/internal/repository/payment_due_notification_excluded_buyer"
	service "content-service/internal/service/payment_due_notification"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewPaymentDueNotificationRepository(db)
	repoExcludedBuyer := repositoryExcludedBuyer.NewPaymentDueNotificationExcludedBuyerRepository(db)
	service := service.NewPaymentDueNotificationService(repo, repoExcludedBuyer)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("payment-due-notifications")
	route.Post("/search", h.GetPaymentDueNotification)
	route.Get("/:id", h.GetPaymentDueNotificationByID)
	route.Post("/", h.CreatePaymentDueNotification)
	route.Put("/:id", h.UpdatePaymentDueNotification)
	route.Put("/status/:id", h.UpdatePaymentDueNotificationStatus)
	route.Delete("/:id", h.DeletePaymentDueNotification)
}
