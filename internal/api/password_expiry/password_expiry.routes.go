package passwordexpiry

import (
	"content-service/internal/global"
	repository "content-service/internal/repository/password_expiry"
	service "content-service/internal/service/password_expiry"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func Register(r fiber.Router, db *gorm.DB, config *global.Config) {
	repo := repository.NewPasswordExpiryRepository(db)
	service := service.NewPasswordExpiryService(repo)
	handler := &Handler{Service: service}

	SetupRoutes(r, handler)
}

func SetupRoutes(r fiber.Router, h *Handler) {
	route := r.Group("password-expiries")
	route.Get("/", h.GetPasswordExpiry)
	route.Put("/:id", h.UpdatePasswordExpiry)
}
