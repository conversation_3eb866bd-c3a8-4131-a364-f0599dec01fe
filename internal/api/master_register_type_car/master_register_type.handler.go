package masterassetgroup

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/model/dto"
	service "content-service/internal/service/master_register_type_car"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service   service.MasterRegisterTypeCarService
	ErpConfig global.ErpConfig
}

func (h *Handler) SearchMasterRegisterTypeCarFilter(c *fiber.Ctx) error {
	var req dto.MasterRegisterTypeCarPageReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.SearchMasterRegisterTypeCarFilter(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) UpdateMasterRegisterTypeCarStatus(c *fiber.Ctx) error {
	var req dto.MasterRegisterTypeCarUpdateReqDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err = h.Service.UpdateMasterRegisterTypeCarStatus(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) SyncMasterRegisterTypeCarFromErp(c *fiber.Ctx) error {
	var req model.BaseDtoActionBy

	actionBy := util.GetActionByFromHeader(c)
	req.ActionBy = actionBy

	err := h.Service.SyncMasterRegisterTypeCarFromErp(req.ActionBy, h.ErpConfig)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}
