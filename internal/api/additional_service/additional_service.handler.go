package additionalservice

import (
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	service "content-service/internal/service/additional_service"
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"
)

type Handler struct {
	Service service.AdditionalServiceService
}

func (h *Handler) GetAdditionalService(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAdditionalService(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) GetAdditionalServiceByID(c *fiber.Ctx) error {
	idStr := c.<PERSON>("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAdditionalServiceByID(id)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}

func (h *Handler) CreateAdditionalService(c *fiber.Ctx) error {
	var req dto.AdditionalServiceDto

	// 1. Parse JSON string field
	jsonStr := c.FormValue("data")
	if jsonStr == "" {
		return fiber.NewError(fiber.StatusBadRequest, "missing 'data' field")
	}

	if err := json.Unmarshal([]byte(jsonStr), &req); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "invalid JSON: "+err.Error())
	}

	// 2. Parse files (support multiple files)
	// form, err := c.MultipartForm()
	// if err != nil {
	// 	return fiber.NewError(fiber.StatusBadRequest, "failed to parse multipart form: "+err.Error())
	// }

	actionBy := util.GetActionByFromHeader(c)
	err := h.Service.CreateAdditionalService(req, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateAdditionalService(c *fiber.Ctx) error {
	var req dto.AdditionalServiceDto

	// 1. Parse JSON string field
	jsonStr := c.FormValue("data")
	if jsonStr == "" {
		return fiber.NewError(fiber.StatusBadRequest, "missing 'data' field")
	}

	if err := json.Unmarshal([]byte(jsonStr), &req); err != nil {
		return fiber.NewError(fiber.StatusBadRequest, "invalid JSON: "+err.Error())
	}

	// 2. Parse files (support multiple files)
	// form, err := c.MultipartForm()
	// if err != nil {
	// 	return fiber.NewError(fiber.StatusBadRequest, "failed to parse multipart form: "+err.Error())
	// }

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	err = h.Service.UpdateAdditionalService(req, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) UpdateAdditionalServiceStatus(c *fiber.Ctx) error {
	var req dto.AdditionalServiceDto

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}
	req.Id = id

	actionBy := util.GetActionByFromHeader(c)
	err = h.Service.UpdateAdditionalServiceStatus(req, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) DeleteAdditionalService(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	actionBy := util.GetActionByFromHeader(c)
	err = h.Service.DeleteAdditionalService(id, actionBy)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse[any](nil))
}

func (h *Handler) GetAdditionalServiceInterest(c *fiber.Ctx) error {
	var req model.PagingRequest

	if err := c.BodyParser(&req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	res, err := h.Service.GetAdditionalServiceInterest(req)
	if err != nil {
		return err
	}
	return c.Status(http.StatusOK).JSON(model.SuccessResponse(&res))
}
