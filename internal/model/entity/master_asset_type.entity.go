package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetType struct {
	*model.BaseEntity
	AssetTypeCode          *string                `column:"asset_type_code" json:"assetTypeCode"`
	DescriptionTh          *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn          *string                `column:"description_en" json:"descriptionEn"`
	IsActive               bool                   `column:"is_active" json:"isActive"`
	CompanyCode            *string                `column:"company_code" json:"companyCode"`
	AssessmentTemplateCode *string                `column:"assessment_template_code" json:"assessmentTemplateCode"`
	NoOfDayAssessment      *int                   `column:"no_of_day_assessment" json:"noOfDayAssessment"`
	MinimumDepositType     *string                `column:"minimum_deposit_type" json:"minimumDepositType"`
	MinimumDepositRate     *float64               `column:"minimum_deposit_rate" json:"minimumDepositRate"`
	MinimumDepositAmount   *float64               `column:"minimum_deposit_amount" json:"minimumDepositAmount"`
	ReceiveAssetReport     *string                `column:"receive_asset_report" json:"receiveAssetReport"`
	IsDeletedByErp         bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate         *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser            *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser            *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterAssetType) TableName() string {
	return "master_asset_type"
}
