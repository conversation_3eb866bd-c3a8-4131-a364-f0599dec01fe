package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterPrefixName struct {
	*model.BaseEntity
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	PrefixNameCode *string                `column:"prefix_name_code" json:"prefixNameCode"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterPrefixName) TableName() string {
	return "master_prefix_name"
}
