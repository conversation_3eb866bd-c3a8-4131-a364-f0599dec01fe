package entity

import "backend-common-lib/model"

type ConfigParameter struct {
	*model.BaseEntity
	ParameterModule *string `column:"parameter_module" json:"parameterModule"`
	ParameterName   *string `column:"parameter_name" json:"parameterName"`
	LotId           *int    `column:"lot_id" json:"lotId"`
	ValueString     *string `column:"value_string" json:"valueString"`
	ValueInt        *int    `column:"value_int" json:"valueInt"`
	ValueString2    *string `column:"value_string2" json:"valueString2"`
}

func (ConfigParameter) TableName() string {
	return "config_parameters"
}

type ConfigParameterForJoin struct {
	ID              int     `column:"id" json:"id"`
	ParameterModule *string `column:"parameter_module" json:"parameterModule"`
	ParameterName   *string `column:"parameter_name" json:"parameterName"`
	ValueString     *string `column:"value_string" json:"valueString"`
	ValueInt        *int    `column:"value_int" json:"valueInt"`
	ValueString2    *string `column:"value_string2" json:"valueString2"`
}

func (ConfigParameterForJoin) TableName() string {
	return "config_parameters"
}
