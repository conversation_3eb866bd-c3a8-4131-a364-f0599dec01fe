package entity

import (
	"backend-common-lib/model"
)

type BuyerDirector struct {
	*model.BaseEntity
	PrefixNameID         int    `gorm:"column:prefix_name_id;not null" json:"prefix_name_id"`
	FirstName            string `gorm:"column:first_name;size:100;not null" json:"first_name"`
	LastName             string `gorm:"column:last_name;size:100;not null" json:"last_name"`
	MiddleName           string `gorm:"column:middle_name;size:100" json:"middle_name"`
	IdentificationNumber string `gorm:"column:identification_number;size:20;not null" json:"identification_number"`
	FileName             string `gorm:"column:filename;size:100;not null" json:"filename"`
	FileType             string `gorm:"column:file_type;size:50;not null" json:"file_type"`
	FileCategory         string `gorm:"column:file_category;size:50;not null" json:"file_category"`
	Bytes                []byte `gorm:"column:bytes;not null" json:"bytes"`
	BuyerID              int    `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsDelete             bool   `gorm:"column:is_delete;default:false;not null" json:"is_delete"`
}

func (BuyerDirector) TableName() string {
	return "buyer_directors"
}
