package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AdditionalServiceInterest struct {
	*model.BaseEntity
	AdditionalServiceId *int    `gorm:"column:additional_service_id" json:"additionalServiceId"`
	ServiceName         *string `gorm:"column:service_name;->" json:"serviceName"` // from joined AdditionalService

	Status *string `gorm:"column:status" json:"status"`
	Remark *string `gorm:"column:remark" json:"remark"`

	CreatedBy   *int                   `gorm:"column:created_by" json:"createdBy"`
	BuyerName   *string                `gorm:"column:buyer_name;->" json:"buyerName"`     // from joined Buyer
	BidderId    *string                `gorm:"column:bidder_id;->" json:"bidderId"`       // from joined Buyer
	PhoneNumber *string                `gorm:"column:phone_number;->" json:"phoneNumber"` // from joined Buyer
	Email       *string                `gorm:"column:email;->" json:"email"`              // from joined Buyer
	UpdatedUser *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`

	DeletedDate *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (AdditionalServiceInterest) TableName() string {
	return "additional_service_interest"
}
