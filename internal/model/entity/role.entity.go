package entity

import (
	"backend-common-lib/model"
	"time"
)

type Role struct {
	*model.BaseEntity
	Name        string                 `gorm:"column:name" json:"name"`
	Description *string                `gorm:"column:description" json:"description"`
	Module      *string                `gorm:"column:module" json:"module"`
	DeletedDate *time.Time             `gorm:"column:deleted_date" json:"deletedDate"`
	CreatedUser *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (Role) TableName() string {
	return "roles"
}
