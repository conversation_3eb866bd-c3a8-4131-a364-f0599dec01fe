package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterCity struct {
	*model.BaseEntity
	CityCode       *string                `column:"city_code" json:"cityCode"`
	RegionCode     *string                `column:"region_code" json:"regionCode"`
	DescriptionTh  *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn  *string                `column:"description_en" json:"descriptionEn"`
	Initial        *string                `column:"initial" json:"initial"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	CountryCode    *string                `column:"country_code" json:"countryCode"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterCity) TableName() string {
	return "master_city"
}
