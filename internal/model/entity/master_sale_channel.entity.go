package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterSaleChannel struct {
	*model.BaseEntity
	CompanyCode                       *string                `column:"company_code" json:"companyCode" example:"Buyer"`
	SaleChannelCode                   *string                `column:"sale_channel_code" json:"saleChannelCode"`
	DescriptionTh                     *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn                     *string                `column:"description_en" json:"descriptionEn"`
	AuctionExcludeValidateReturnBoard bool                   `column:"auction_exclude_validate_return_board" json:"auctionExcludeValidateReturnBoard"`
	AllowRefund                       bool                   `column:"allow_refund" json:"allowRefund"`
	IsActive                          bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp                    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate                    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser                       *model.EmployeeFor<PERSON>oin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser                       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterSaleChannel) TableName() string {
	return "master_sale_channel"
}
