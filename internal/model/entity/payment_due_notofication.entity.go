package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type PaymentDueNotification struct {
	*model.BaseEntity
	CustomerGroupId   *int                   `column:"customer_group_id" json:"customerGroupId" example:"1"`
	DaysBeforeDue     *int                   `column:"days_before_due" json:"daysBeforeDue" example:"7"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	DeletedAt         *gorm.DeletedAt        `gorm:"column:deleted_at" json:"deletedAt"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
	CustomerGroupDesc *string                `gorm:"column:customer_group_desc;->" json:"customerGroupDesc"`
	DaysBeforeDueTh   *string                `gorm:"column:days_before_due_th;->" json:"daysBeforeDueTh"`
	DaysBeforeDueEn   *string                `gorm:"column:days_before_due_en;->" json:"daysBeforeDueEn"`
}

func (PaymentDueNotification) TableName() string {
	return "payment_due_notification"
}
