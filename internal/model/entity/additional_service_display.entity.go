package entity

import (
	"gorm.io/gorm"
)

type AdditionalServiceDisplay struct {
	ID                  *int            `column:"id" json:"id"`
	AdditionalServiceId *int            `column:"additional_service_id" json:"additionalServiceId"`
	DisplayId           *int            `column:"display_id" json:"displayId"`
	DisplayDescTh       *string         `gorm:"column:display_desc_th;->" json:"displayDescTh"`
	DisplayDescEn       *string         `gorm:"column:display_desc_en;->" json:"displayDescEn"`
	DeletedDate         *gorm.DeletedAt `gorm:"column:deleted_date" json:"deletedDate"`
}

func (AdditionalServiceDisplay) TableName() string {
	return "additional_service_display"
}
