package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterVendorGroup struct {
	*model.BaseEntity
	VendorGroupCode *string                `column:"vendor_group_code" json:"vendorGroupCode"`
	DescriptionTh   *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEn   *string                `column:"description_en" json:"descriptionEn"`
	IsActive        bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp  bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate  *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser     *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser     *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterVendorGroup) TableName() string {
	return "master_vendor_group"
}
