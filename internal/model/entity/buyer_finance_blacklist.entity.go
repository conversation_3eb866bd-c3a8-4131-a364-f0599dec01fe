package entity

type BuyerFinanceBlacklist struct {
	Id            int  `gorm:"primaryKey" column:"id" json:"id" ignore:"true"`
	BuyerID       int  `gorm:"column:buyer_id;not null" json:"buyer_id"`
	VendorGroupID int  `gorm:"column:vendor_group_id;not null" json:"vendor_group_id"`
	IsDeleted     bool `gorm:"column:is_deleted;default:false;not null" json:"is_deleted"`
}

func (BuyerFinanceBlacklist) TableName() string {
	return "buyer_finance_blacklist"
}
