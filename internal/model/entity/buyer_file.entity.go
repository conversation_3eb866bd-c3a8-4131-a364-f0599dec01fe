package entity

import (
	"backend-common-lib/model"
)

type BuyerFile struct {
	*model.BaseEntity
	FileName     string `gorm:"column:filename;size:100;not null" json:"filename"`
	FileType     string `gorm:"column:file_type;size:50;not null" json:"file_type"`
	FileCategory string `gorm:"column:file_category;size:50;not null" json:"file_category"`
	Bytes        []byte `gorm:"column:bytes;not null" json:"bytes"`
	BuyerID      int    `gorm:"column:buyer_id;not null" json:"buyer_id"`
	IsDelete     bool   `gorm:"column:is_delete;default:false;not null" json:"is_delete"`
}

func (BuyerFile) TableName() string {
	return "buyer_files"
}
