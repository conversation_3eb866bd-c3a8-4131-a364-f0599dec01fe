package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterDepartment struct {
	*model.BaseEntity
	DepartmentCode   *string                `column:"department_code" json:"departmentCode"`
	DepartmentNameTh *string                `column:"department_name_th" json:"departmentNameTh"`
	DepartmentNameEn *string                `column:"department_name_en" json:"departmentNameEn"`
	IsActive         bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp   bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate   *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser      *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser      *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterDepartment) TableName() string {
	return "master_department"
}
