package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetLocationFloor struct {
	*model.BaseEntity
	LocationFloorCode *string                `column:"location_floor_code" json:"locationFloorCode"`
	Floor             *string                `column:"floor" json:"floor"`
	DescriptionTH     *string                `column:"description_th" json:"descriptionTh"`
	DescriptionEN     *string                `column:"description_en" json:"descriptionEn"`
	BranchCode        *string                `column:"branch_code" json:"branchCode"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterAssetLocationFloor) TableName() string {
	return "master_asset_location_floor"
}
