package entity

import (
	"backend-common-lib/model"

	"gorm.io/gorm"
)

type AdditionalServiceAsset struct {
	ID                  *int                     `column:"id" json:"id"`
	AdditionalServiceId *int                     `column:"additional_service_id" json:"additionalServiceId"`
	AssetTypeId         *int                     `column:"asset_type_id" json:"assetTypeId"`
	AssetGroupId        *int                     `column:"asset_group_id" json:"assetGroupId"`
	DeletedDate         *gorm.DeletedAt          `gorm:"column:deleted_date" json:"deletedDate"`
	AssetType           *model.AssetTypeForJoin  `gorm:"foreignKey:AssetTypeId"`
	AssetGroup          *model.AssetGroupForJoin `gorm:"foreignKey:AssetGroupId"`
}

func (AdditionalServiceAsset) TableName() string {
	return "additional_service_asset"
}
