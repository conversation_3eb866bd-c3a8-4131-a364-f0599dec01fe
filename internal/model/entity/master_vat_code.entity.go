package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterVatCode struct {
	*model.BaseEntity
	CompanyCode    *string                `column:"company_code" json:"companyCode"`
	VatCode        *string                `column:"vat_code" json:"vatCode"`
	Description    *string                `column:"description" json:"description"`
	Vat            *int                   `column:"vat" json:"vat"`
	IsActive       bool                   `column:"is_active" json:"isActive"`
	IsDeletedByErp bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterVatCode) TableName() string {
	return "master_vat_code"
}
