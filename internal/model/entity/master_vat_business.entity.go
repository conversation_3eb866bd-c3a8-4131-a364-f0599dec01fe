package entity

import (
	"backend-common-lib/model"
	"time"
)

type MasterVatBusiness struct {
	*model.BaseEntity
	VatBusinessCode   *string                `column:"vat_business_code" json:"vatBusinessCode"`
	Description       *string                `column:"description" json:"description"`
	TaxEstablishment  *string                `column:"tax_establishment" json:"taxEstablishment"`
	TaxBranchNo       *string                `column:"tax_branch_no" json:"taxBranchNo"`
	CompanyName       *string                `column:"company_name" json:"companyName"`
	CompanyName2      *string                `column:"company_name2" json:"companyName2"`
	CompanyAddress    *string                `column:"company_address" json:"CompanyAddress"`
	CompanyAddress2   *string                `column:"company_address2" json:"CompanyAddress2"`
	CompanyAddress3   *string                `column:"company_address3" json:"CompanyAddress3"`
	VatRegistrationNo *string                `column:"vat_registration_no" json:"vatRegistrationNo"`
	PhoneNo           *string                `column:"phone_no" json:"phoneNo"`
	FaxNo             *string                `column:"fax_no" json:"faxNo"`
	IsActive          bool                   `column:"is_active" json:"isActive"`
	CompanyCode       *string                `column:"company_code" json:"companyCode"`
	IsDeletedByErp    bool                   `column:"is_deleted_by_erp" json:"isDeletedByErp"`
	LatestSyncDate    *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser       *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser       *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterVatBusiness) TableName() string {
	return "master_vat_business"
}
