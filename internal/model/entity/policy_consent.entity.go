package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

type PolicyConsent struct {
	*model.BaseEntity
	ConsentName  *string                `column:"consent_name" json:"consentName"`
	ConsentType  *string                `column:"consent_type" json:"consentType"`
	Description  *string                `column:"description" json:"description"`
	VersionMajor *int                   `column:"version_major" json:"versionMajor"`
	VersionMinor *int                   `column:"version_minor" json:"versionMinor"`
	StartDate    *time.Time             `column:"start_date" json:"startDate"`
	EndDate      *time.Time             `column:"end_date" json:"endDate"`
	PolicyTextTh *string                `column:"policy_text_th" json:"policyTextTh"`
	PolicyTextEn *string                `column:"policy_text_en" json:"policyTextEn"`
	IsActive     bool                   `column:"is_active" json:"isActive"`
	CreatedUser  *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser  *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
	DeletedUser  *model.EmployeeForJoin `gorm:"foreignKey:DeletedBy;references:UserID"`
	DeletedBy    *int                   `column:"deleted_by" json:"deletedBy" ignore:"true"`
	DeletedAt    *gorm.DeletedAt        `gorm:"column:deleted_at" json:"deletedAt"`
}

func (PolicyConsent) TableName() string {
	return "policy_consent"
}

type PolicyConsentLog struct {
	*PolicyConsent
	PolicyConsentId int `column:"policy_consent_id" json:"policyConsentId"`
}

func (PolicyConsentLog) TableName() string {
	return "policy_consent_log"
}
