package entity

import (
	"backend-common-lib/model"
	"time"

	"gorm.io/gorm"
)

// Role
// @Description A representation of a user.
// @ID Role

type MasterAuctionType struct {
	*model.BaseEntity
	Code           *int                   `column:"code" json:"code" example:"1"`
	Name           *string                `column:"product_name" json:"productName" example:"car"`
	Description    *string                `column:"description" json:"description" example:"1"`
	DeletedAt      *gorm.DeletedAt        `column:"deleted_at" json:"deletedAt"`
	LatestSyncDate *time.Time             `column:"latest_sync_date" json:"latestSyncDate"`
	CreatedUser    *model.EmployeeForJoin `gorm:"foreignKey:CreatedBy;references:UserID"`
	UpdatedUser    *model.EmployeeForJoin `gorm:"foreignKey:UpdatedBy;references:UserID"`
}

func (MasterAuctionType) TableName() string {
	return "master_auction_type"
}
