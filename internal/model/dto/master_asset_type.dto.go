package dto

import (
	"backend-common-lib/model"
	"time"
)

type AllMasterAssetTypeDto struct {
	Id             *int                `json:"id"`
	AssetTypeCode  *string             `json:"assetTypeCode" example:"Buyer"`
	AssetGroupCode *string             `json:"assetGroupCode"`
	DescriptionTh  *string             `json:"descriptionTh"`
	DescriptionEn  *string             `json:"descriptionEn"`
	IsActive       bool                `json:"isActive"`
	Type           *string             `json:"type"`
	AssetGroupList []AssetGroupListDto `json:"assetGroupList"`
}

type MasterAssetTypeDto struct {
	model.BaseDto
	AssetTypeCode          *string  `json:"assetTypeCode" example:"Buyer"`
	DescriptionTh          *string  `json:"descriptionTh"`
	DescriptionEn          *string  `json:"descriptionEn"`
	IsActive               bool     `json:"isActive"`
	CompanyCode            *string  `json:"companyCode"`
	AssessmentTemplateCode *string  `json:"assessmentTemplateCode"`
	NoOfDayAssessment      *int     `json:"noOfDayAssessment"`
	MinimumDepositType     *string  `json:"minimumDepositType"`
	MinimumDepositRate     *float64 `json:"minimumDepositRate"`
	MinimumDepositAmount   *float64 `json:"minimumDepositAmount"`
	ReceiveAssetReport     *string  `json:"receiveAssetReport"`
	IsDeletedByErp         bool     `json:"isDeletedByErp"`
}

type AssetGroupListDto struct {
	Id             *int    `json:"id"`
	AssetTypeCode  *string `json:"assetTypeCode" example:"Buyer"`
	AssetGroupCode *string `json:"assetGroupCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	Type           *string `json:"type"`
}

type AllMasterAssetTypeListDto struct {
	AssetTypeMasterList []AllMasterAssetTypeDto `json:"assetTypeMasterList"`
}

type MasterAssetTypePageReqDto struct {
	AssetTypeCode *string `json:"assetTypeCode" example:"Buyer"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterAssetTypePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterAssetTypeSyncErpRespDto struct {
	CompanyCode            *string  `json:"CompanyCode"`
	AssetTypeCode          *string  `json:"Asset_Type_Code"`
	DescriptionTh          *string  `json:"Description_TH"`
	DescriptionEn          *string  `json:"Description_EN"`
	Status                 string   `json:"Status"`
	AssessmentTemplateCode *string  `json:"Assessment_Template_Code"`
	NoOfDayAssessment      *int     `json:"No_of_day_assessment"`
	MinimumDepositType     *string  `json:"Minimum_Deposit_Type"`
	MinimumDepositRate     *float64 `json:"Minimum_Deposit_Rate"`
	MinimumDepositAmount   *float64 `json:"Minimum_Deposit_Amount"`
	ReceiveAssetReport     *string  `json:"Receive_Asset_Report"`
}

type MasterAssetTypeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
