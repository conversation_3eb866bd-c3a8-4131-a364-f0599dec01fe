package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterPaymentMethodPageReqDto struct {
	PaymentMethodCode *string `json:"paymentMethodCode"`
	DescriptionTh     *string `json:"descriptionTh"`
	DescriptionEn     *string `json:"descriptionEn"`
	IsActive          *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterPaymentMethodPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterPaymentMethodDto struct {
	model.BaseDto
	CompanyCode               *string `json:"companyCode"`
	PaymentMethodCode         *string `json:"paymentMethodCode"`
	DescriptionTh             *string `json:"descriptionTh"`
	DescriptionEn             *string `json:"descriptionEn"`
	Type                      *string `json:"type"`
	IsExcludeCalSellerPayment bool    `json:"isExcludeCalSellerPayment"`
	IsActive                  bool    `json:"isActive"`
	IsDeletedByErp            bool    `json:"isDeletedByErp"`
}

type MasterPaymentMethodSyncErpRespDto struct {
	CompanyCode               *string `json:"CompanyCode"`
	PaymentMethodCode         *string `json:"Payment_Method_Code"`
	DescriptionTh             *string `json:"Description_TH"`
	DescriptionEn             *string `json:"Description_EN"`
	Type                      *string `json:"Type"`
	IsExcludeCalSellerPayment bool    `json:"Exclude_Calc_Seller_Payment"`
	Status                    string  `json:"Status"`
}

type MasterPaymentMethodUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
