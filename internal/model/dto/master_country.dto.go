package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCountryPageReqDto struct {
	CountryCode   *string `json:"countryCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCountryPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCountryDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	CountryCode    *string `json:"countryCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterCountrySyncErpRespDto struct {
	CompanyCode   *string `json:"CompanyCode"`
	CountryCode   *string `json:"Country_Code"`
	DescriptionTh *string `json:"Description_TH"`
	DescriptionEn *string `json:"Description_EN"`
	Status        string  `json:"Status"`
}

type MasterCountryUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
