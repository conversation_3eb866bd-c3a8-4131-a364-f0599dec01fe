package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterSellerOfferDto struct {
	model.BaseDto
	CompanyCode     *string `json:"companyCode"`
	SellerOfferCode *string `json:"sellerOfferCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	Split           bool    `json:"split"`
	Invoice1        *int    `json:"invoice1"`
	Invoice2        *int    `json:"invoice2"`
	VendorCode      *string `json:"vendorCode"`
	IsActive        bool    `json:"isActive"`
	IsDeletedByErp  bool    `json:"isDeletedByErp"`
}

type MasterSellerOfferPageReqDto struct {
	SellerOfferCode *string `json:"sellerOfferCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterSellerOfferPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterSellerOfferSyncErpRespDto struct {
	CompanyCode     *string `json:"CompanyCode"`
	SellerOfferCode *string `json:"Seller_Offer_Code"`
	DescriptionTh   *string `json:"Description_TH"`
	DescriptionEn   *string `json:"Description_EN"`
	Split           bool    `json:"Split"`
	Invoice1        *int    `json:"Invoice_1"`
	Invoice2        *int    `json:"Invoice_2"`
	SellerCode      *string `json:"Seller_Code"`
	VendorCode      *string `json:"Vendor_Code"`
	IsActive        *bool   `json:"isActive"`
	Status          string  `json:"Status"`
}

type MasterSellerOfferUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
