package dto

import (
	"backend-common-lib/model"
	"time"
)

type AdditionalServiceDto struct {
	model.BaseDto
	ServiceName             *string                 `json:"serviceName"`
	Detail                  *string                 `json:"detail"`
	ServiceTypeTh           *string                 `json:"serviceTypeTh"`
	ServiceTypeEn           *string                 `json:"serviceTypeEn"`
	ServiceTypeId           *int                    `json:"serviceTypeId"`
	StartDate               *time.Time              `json:"startDate"`
	EndDate                 *time.Time              `json:"endDate"`
	ServiceAssetType        []*ServiceAssetType     `json:"serviceAssetType"`
	ServiceAssetGroup       []*ServiceAssetGroup    `json:"serviceAssetGroup"`
	ServiceCustomerGroup    []*ServiceCustomerGroup `json:"serviceCustomerGroup"`
	ServiceProvider         []*ServiceProvider      `json:"serviceProvider"`
	ServiceDisplay          []*ServiceDisplay       `json:"serviceDisplay"`
	ServiceAssetTypeIds     []*int                  `json:"serviceAssetTypeIds"`     //NOTE - only for FE to easily map to dropdown
	ServiceAssetGroupIds    []*int                  `json:"serviceAssetGroupIds"`    //NOTE - only for FE to easily map to dropdown
	ServiceCustomerGroupIds []*int                  `json:"serviceCustomerGroupIds"` //NOTE - only for FE to easily map to dropdown
	ServiceDisplayIds       []*int                  `json:"serviceDisplayIds"`       //NOTE - only for FE to easily map to dropdown
	IsActive                bool                    `json:"isActive"`
}

type ServiceAssetType struct {
	AssetTypeId     *int    `json:"assetTypeId"`
	AssetTypeDescTh *string `json:"assetTypeDescTh"`
	AssetTypeDescEn *string `json:"assetTypeDescEn"`
}

type ServiceAssetGroup struct {
	AssetTypeId      *int    `json:"assetTypeId"`
	AssetGroupId     *int    `json:"assetGroupId"`
	AssetGroupDescTh *string `json:"assetGroupDescTh"`
	AssetGroupDescEn *string `json:"assetGroupDescEn"`
}

type ServiceCustomerGroup struct {
	CustomerGroupId   *int    `json:"customerGroupId"`
	CustomerGroupDesc *string `json:"customerGroupDesc"`
}

type ServiceProvider struct {
	ProviderName *string `json:"providerName"`
	ProviderLink *string `json:"providerLink"`
}

type ServiceDisplay struct {
	DisplayId     *int    `json:"displayId"`
	DisplayDescTh *string `json:"displayDescTh"`
	DisplayDescEn *string `json:"displayDescEn"`
}

type AdditionalServiceInterestDto struct {
	model.BaseDto
	BidderId         *string             `json:"bidderId"`
	BuyerName        *string             `json:"buyerName"`
	PhoneNumber      *string             `json:"phoneNumber"`
	Email            *string             `json:"email"`
	ServiceName      *string             `json:"serviceName"`
	Status           *string             `json:"status"`
	Remark           *string             `json:"remark"`
	ServiceAssetType []*ServiceAssetType `json:"serviceAssetType"`
}
