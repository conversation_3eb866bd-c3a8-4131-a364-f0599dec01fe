package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterVatCodeDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	VatCode        *string `json:"vatCode"`
	Description    *string `json:"description"`
	Vat            int     `json:"vat"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterVatCodePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterVatCodeListDto struct {
	MasterVatCodeList []MasterVatCodeDto `json:"data"`
}

type MasterVatCodePageReqDto struct {
	VatCode     *string `json:"vatCode"`
	Description *string `json:"Description"`
	Vat         *int    `json:"vat"`
	IsActive    *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterVatCodeSyncErpRespDto struct {
	CompanyCode *string `json:"CompanyCode"`
	VatCode     *string `json:"VAT_Code"`
	Description *string `json:"Description"`
	Vat         *int    `json:"VAT"`
	Status      *string `json:"Status"`
}

type MasterVatCodeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
