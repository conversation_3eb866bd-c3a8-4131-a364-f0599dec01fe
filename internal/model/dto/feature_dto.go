package dto

type FeatureDto struct {
	Id                int                `json:"id"`
	FeatureName       *string            `json:"featureName"`
	Sequence          *int               `json:"sequence"`
	FeatureActionList []FeatureActionDto `json:"featureActionList"`
	FeatureList       []FeatureDto       `json:"featureList"`
}

type FeatureListDto struct {
	FeatureList []FeatureDto `json:"featureList"`
}
