package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterPostcodePageReqDto struct {
	RegionCode      *string `json:"regionCode"`
	CityCode        *string `json:"cityCode"`
	DistrictCode    *string `json:"districtCode"`
	SubDistrictCode *string `json:"subDistrictCode"`
	PostCode        *string `json:"postCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterPostcodePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterPostcodeDto struct {
	model.BaseDto
	CountryCode     *string `json:"countryCode"`
	RegionCode      *string `json:"regionCode"`
	CityCode        *string `json:"cityCode"`
	DistrictCode    *string `json:"districtCode"`
	SubDistrictCode *string `json:"subDistrictCode"`
	PostCode        *string `json:"postCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	IsActive        bool    `json:"isActive"`
	IsDeletedByErp  bool    `json:"isDeletedByErp"`
}

type MasterPostcodeSyncErpRespDto struct {
	CountryCode     *string `json:"Country_Code"`
	RegionCode      *string `json:"Region_Code"`
	CityCode        *string `json:"City_Code"`
	DistrictCode    *string `json:"District"`
	SubDistrictCode *string `json:"Sub_District"`
	PostCode        *string `json:"Postcode"`
	DescriptionTh   *string `json:"Description_TH"`
	DescriptionEn   *string `json:"Description_EN"`
	Status          string  `json:"Status"`
}

type MasterPostcodeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
