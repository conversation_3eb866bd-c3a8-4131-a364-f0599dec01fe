package dto

import (
	"backend-common-lib/model"
	"time"
)

type PolicyConsentDto struct {
	model.BaseDto
	ConsentName  *string    `json:"consentName"`
	ConsentType  *string    `json:"consentType"`
	Description  *string    `json:"description"`
	VersionMajor *int       `json:"versionMajor"`
	VersionMinor *int       `json:"versionMinor"`
	Version      *string    `json:"version"`
	StartDate    *time.Time `json:"startDate"`
	EndDate      *time.Time `json:"endDate"`
	PolicyTextTh *string    `json:"policyTextTh"`
	PolicyTextEn *string    `json:"policyTextEn"`
	IsActive     bool       `json:"isActive"`
}

type PolicyConsentReqDto struct {
	PolicyConsentDto
	ActionBy *int `json:"actionBy"`
}

type PolicyPageResDto struct {
	model.PagingModel[PolicyConsentDto]
	MaxVersionMajor *int `json:"maxVersionMajor"`
}
