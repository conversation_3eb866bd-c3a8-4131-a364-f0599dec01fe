package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterBranchDto struct {
	model.BaseDto
	BranchCode        *string `json:"branchCode"`
	DescriptionTh     *string `json:"descriptionTh"`
	DescriptionEn     *string `json:"descriptionEn"`
	CountryCode       *string `json:"countryCode"`
	RegionCode        *string `json:"regionCode"`
	CityCode          *string `json:"cityCode"`
	PostCode          *string `json:"postCode"`
	VATBusinessCode   *string `json:"vatBusinessCode"`
	AssetLocationCode *string `json:"assetLocationCode"`
	IsActive          bool    `json:"isActive"`
	CompanyCode       *string `json:"companyCode"`
	IsDeletedByErp    bool    `json:"isDeletedByErp"`
}

type MasterBranchPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterBranchSyncErpRespDto struct {
	CompanyCode       *string `json:"CompanyCode"`
	BranchCode        *string `json:"Branch_Code"`
	DescriptionTh     *string `json:"Description_TH"`
	DescriptionEn     *string `json:"Description_EN"`
	CountryCode       *string `json:"Country_Code"`
	RegionCode        *string `json:"Region_Code"`
	CityCode          *string `json:"City_Code"`
	PostCode          *string `json:"Postcode"`
	VATBusinessCode   *string `json:"VAT_Business"`
	AssetLocationCode *string `json:"Asset_Location_Code"`
	Status            string  `json:"Status"`
}

type MasterBranchPageReqDto struct {
	BranchCode      *string `json:"branchCode"`
	DescriptionTh   *string `json:"descriptionTh"`
	DescriptionEn   *string `json:"descriptionEn"`
	RegionCode      *string `json:"regionCode"`
	CityCode        *string `json:"cityCode"`
	VATBusinessCode *string `json:"vatBusinessCode"`
	IsActive        *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterBranchListDto struct {
	MasterBranchList []MasterBranchDto `json:"branchMasterList"`
}

type MasterBranchUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
