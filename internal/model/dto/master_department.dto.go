package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterDepartmentDto struct {
	model.BaseDto
	DepartmentCode   *string `json:"departmentCode"`
	DepartmentNameTh *string `json:"departmentNameTh"`
	DepartmentNameEn *string `json:"departmentNameEn"`
	IsActive         bool    `json:"isActive"`
	IsDeletedByErp   bool    `json:"isDeletedByErp"`
}

type MasterDepartmentPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterDepartmentListDto struct {
	MasterDepartmentList []MasterDepartmentDto `json:"data"`
}

type MasterDepartmentSyncErpRespDto struct {
	DepartmentCode   *string `json:"DepartmentCode"`
	DepartmentNameTh *string `json:"DepartmentName_TH"`
	DepartmentNameEn *string `json:"DepartmentName_EN"`
	Status           string  `json:"Status"`
}

type MasterDepartmentPageReqDto struct {
	DepartmentCode   *string `json:"departmentCode"`
	DepartmentNameTh *string `json:"departmentNameTh"`
	DepartmentNameEn *string `json:"departmentNameEn"`
	IsActive         *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterDepartmentUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
