package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterDistrictDto struct {
	model.BaseDto
	RegionCode     *string `json:"regionCode"`
	CityCode       *string `json:"cityCode"`
	DistrictCode   *string `json:"districtCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterDistrictPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterDistrictListDto struct {
	MasterDistrictList []MasterDistrictDto `json:"data"`
}

type MasterDistrictSyncErpRespDto struct {
	RegionCode    *string `json:"Region_Code"`
	CityCode      *string `json:"City_Code"`
	DistrictCode  *string `json:"District"`
	DescriptionTh *string `json:"Description_TH"`
	DescriptionEn *string `json:"Description_EN"`
	Status        string  `json:"Status"`
}

type MasterDistrictPageReqDto struct {
	RegionCode    *string `json:"regionCode"`
	CityCode      *string `json:"cityCode"`
	DistrictCode  *string `json:"districtCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterDistrictUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
