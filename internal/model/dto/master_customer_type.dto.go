package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerTypeDto struct {
	model.BaseDto
	CustomerTypeCode *string `json:"customerTypeCode"`
	DescriptionTh    *string `json:"descriptionTh"`
	DescriptionEn    *string `json:"descriptionEn"`
	IsWHT            bool    `json:"isWHT"`
	IsActive         bool    `json:"isActive"`
	IsDeletedByErp   bool    `json:"isDeletedByErp"`
}

type MasterCustomerTypePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCustomerTypePageReqDto struct {
	CustomerTypeCode *string `json:"customerTypeCode"`
	DescriptionTh    *string `json:"descriptionTh"`
	DescriptionEn    *string `json:"descriptionEn"`
	IsWHT            *bool   `json:"isWHT"`
	IsActive         *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCustomerTypeSyncErpRespDto struct {
	CustomerTypeCode *string `json:"Customer_Type_Code"`
	DescriptionTh    *string `json:"Description_TH"`
	DescriptionEn    *string `json:"Description_EN"`
	IsWHT            bool    `json:"WHT"`
	Status           string  `json:"Status"`
}

type MasterCustomerTypeUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
