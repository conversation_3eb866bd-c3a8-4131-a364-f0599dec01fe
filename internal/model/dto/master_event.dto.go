package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterEventDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	EventCode      *string `json:"eventCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterEventPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterEventListDto struct {
	MasterEventList []MasterEventDto `json:"data"`
}

type MasterEventSyncErpRespDto struct {
	CompanyCode   *string `json:"CompanyCode"`
	EventCode     *string `json:"Event_Code"`
	DescriptionTh *string `json:"Description_TH"`
	DescriptionEn *string `json:"Description_EN"`
	Status        string  `json:"Status"`
}

type MasterEventPageReqDto struct {
	EventCode     *string `json:"eventCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterEventUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
