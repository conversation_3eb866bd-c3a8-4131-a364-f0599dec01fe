package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterAssetGroupDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	AssetGroupCode *string `json:"assetGroupCode"`
	AssetTypeCode  *string `json:"assetTypeCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterAssetGroupPageReqDto struct {
	AssetGroupCode *string `json:"assetGroupCode"`
	AssetTypeCode  *string `json:"assetTypeCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterAssetGroupPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterAssetGroupSyncErpRespDto struct {
	CompanyCode    *string `json:"CompanyCode"`
	AssetGroupCode *string `json:"Asset_Group_Code"`
	AssetTypeCode  *string `json:"Asset_Type_Code"`
	DescriptionTh  *string `json:"Description_TH"`
	DescriptionEn  *string `json:"Description_EN"`
	Status         string  `json:"Status"`
}

type MasterAssetGroupUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
