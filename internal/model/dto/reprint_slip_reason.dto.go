package dto

import "backend-common-lib/model"

type ReprintSlipReasonDto struct {
	model.BaseDto
	Reason   string `json:"reason"`
	IsActive bool   `json:"isActive"`
}

type ReprintSlipReasonListDto struct {
	ReprintSlipReasonList []ReprintSlipReasonDto `json:"data"`
}

type ReprintSlipReasonPageReqDto struct {
	model.PagingRequest
}

type ReprintSlipReasonUpdateReqDto struct {
	model.BaseDtoActionBy
	Reason   *string `json:"reason"`
	IsActive bool    `json:"isActive"`
}
