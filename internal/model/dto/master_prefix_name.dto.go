package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterPrefixNamePageReqDto struct {
	PrefixNameCode *string `json:"prefixNameCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterPrefixNamePageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterPrefixNameDto struct {
	model.BaseDto
	CompanyCode    *string `json:"companyCode"`
	PrefixNameCode *string `json:"prefixNameCode"`
	DescriptionTh  *string `json:"descriptionTh"`
	DescriptionEn  *string `json:"descriptionEn"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterPrefixNameSyncErpRespDto struct {
	CompanyCode    *string `json:"CompanyCode"`
	PrefixNameCode *string `json:"Prefix_Name_Code"`
	DescriptionTh  *string `json:"Description_TH"`
	DescriptionEn  *string `json:"Description_EN"`
	Status         string  `json:"Status"`
}

type MasterPrefixNameUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
