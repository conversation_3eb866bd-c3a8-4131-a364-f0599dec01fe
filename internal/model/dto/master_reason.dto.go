package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterReasonPageReqDto struct {
	ReasonCode    *string `json:"reasonCode"`
	DescriptionTh *string `json:"descriptionTh"`
	DescriptionEn *string `json:"descriptionEn"`
	Type          *string `json:"type"`
	Page          *string `json:"page"`
	IsActive      *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterReasonPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterReasonDto struct {
	model.BaseDto
	CompanyCode           *string `json:"companyCode"`
	ReasonCode            *string `json:"reasonCode"`
	DescriptionTh         *string `json:"descriptionTh"`
	DescriptionEn         *string `json:"descriptionEn"`
	SaleProcessStatusCode *string `json:"saleProcessStatusCode"`
	Type                  *string `json:"type"`
	Page                  *string `json:"page"`
	SellerPaymentOption   *string `json:"sellerPaymentOption"`
	IsActive              bool    `json:"isActive"`
	IsDeletedByErp        bool    `json:"isDeletedByErp"`
}

type MasterReasonSyncErpRespDto struct {
	CompanyCode           *string `json:"CompanyCode"`
	ReasonCode            *string `json:"Reason_Code"`
	DescriptionTh         *string `json:"Description_TH"`
	DescriptionEn         *string `json:"Description_EN"`
	SaleProcessStatusCode *string `json:"Sales_Process_Status_Code"`
	Type                  *string `json:"Type"`
	Page                  *string `json:"Page"`
	Status                string  `json:"Status"`
	SellerPaymentOption   *string `json:"Seller_Payment_Option"`
}

type MasterReasonUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
