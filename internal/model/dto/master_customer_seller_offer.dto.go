package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterCustomerSellerOfferDto struct {
	model.BaseDto
	CustomerNo     *string `json:"customerNo"`
	CompanyCode    *string `json:"companyCode"`
	SellerCode     *string `json:"sellerCode"`
	IsActive       bool    `json:"isActive"`
	IsDeletedByErp bool    `json:"isDeletedByErp"`
}

type MasterCustomerSellerOfferPageReqDto struct {
	CustomerNo *string `json:"customerNo"`
	SellerCode *string `json:"sellerCode"`
	IsActive   *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterCustomerSellerOfferPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterCustomerSellerOfferSyncErpRespDto struct {
	CompanyCode *string `json:"CompanyCode"`
	CustomerNo  *string `json:"Customer_No"`
	SellerCode  *string `json:"Seller_Code"`
	IsActive    *bool   `json:"isActive"`
	Status      string  `json:"Status"`
}

type MasterCustomerSellerOfferUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
