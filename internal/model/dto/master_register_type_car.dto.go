package dto

import (
	"backend-common-lib/model"
	"time"
)

type MasterRegisterTypeCarDto struct {
	model.BaseDto
	AssetRegisterTypeCode *string `json:"assetRegisterTypeCode"`
	CompanyCode           *string `json:"companyCode"`
	DescriptionTh         *string `json:"descriptionTh"`
	DescriptionEn         *string `json:"descriptionEn"`
	IsActive              bool    `json:"isActive"`
	IsDeletedByErp        bool    `json:"isDeletedByErp"`
}

type MasterRegisterTypeCarPageReqDto struct {
	AssetRegisterTypeCode *string `json:"assetRegisterTypeCode"`
	DescriptionTh         *string `json:"descriptionTh"`
	DescriptionEn         *string `json:"descriptionEn"`
	IsActive              *bool   `json:"isActive"`
	model.PagingRequest
}

type MasterRegisterTypeCarPageRespDto[T any] struct {
	model.PagingModel[T]
	LatestSyncDate *time.Time `json:"latestSyncDate" example:"2020-01-01"`
}

type MasterRegisterTypeCarSyncErpRespDto struct {
	CompanyCode           *string `json:"CompanyCode"`
	AssetRegisterTypeCode *string `json:"Asset_Register_Type_Code"`
	DescriptionTh         *string `json:"Description_TH"`
	DescriptionEn         *string `json:"Description_EN"`
	Status                string  `json:"Status"`
}

type MasterRegisterTypeCarUpdateReqDto struct {
	model.BaseDtoActionBy
	IsActive bool `json:"isActive"`
}
