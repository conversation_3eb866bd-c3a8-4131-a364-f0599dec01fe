package bootstrap

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/integrates/cache"
	"backend-common-lib/model"
	"content-service/internal/global"
	"content-service/internal/router"
	"context"
	"errors"
	"fmt"
	"strconv"

	jwtware "github.com/gofiber/contrib/jwt"
	"github.com/gofiber/fiber/v2"
	"github.com/gofiber/fiber/v2/log"
	"github.com/gofiber/fiber/v2/middleware/cors"
	"github.com/gofiber/fiber/v2/middleware/logger"
	"github.com/golang-jwt/jwt/v5"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

type AppContext struct {
	App    *fiber.App
	Config *global.Config
	Redis  cache.RedisContext
	// WS     *websockets.WebSocketServer
}

func SetupApp(db *gorm.DB, config *global.Config) *AppContext {
	app := fiber.New(fiber.Config{
		ErrorHandler: func(c *fiber.Ctx, err error) error {

			var appErr *errs.ErrContext
			if errors.As(err, &appErr) {
				log.Errorf("%+v", appErr)
				return c.Status(appErr.HttpCode).JSON(model.ErrorContextResponse(appErr))
			} else {
				log.Errorf("%+v", err)
				return c.Status(fiber.StatusInternalServerError).JSON(model.ErrorResponseWithDefaultMsg(err))
			}
		},
	})

	// Setup redis
	rdb := cache.NewRedisContext(&redis.Options{
		Addr:     config.Redis.Host + ":" + strconv.Itoa(config.Redis.Port),
		Password: config.Redis.Password,
		DB:       config.Redis.DB,
		Protocol: config.Redis.Protocol,
	})

	appCtx := &AppContext{
		App:    app,
		Config: config,
		Redis:  rdb,
	}

	// Setup middleware
	// appCtx.SetupCORS()

	app.Use(logger.New(logger.Config{
		Format: "${pid} [${locals:requestid}] ${status} - ${method} ${path}\n",
	}))

	// appCtx.SetupAuthorization() //FIXME - To delete when client call API gateway

	// Register routes
	api := app.Group("/api/v1/content-service")

	router.RegisterRoutes(api, db, config, rdb)

	apiDropdown := app.Group("/api/v1/dropdown-service")
	router.RegisterDropdownRoutes(apiDropdown, db, config, rdb)

	// Print all routes
	for _, route := range app.GetRoutes() {
		fmt.Printf("%s\t%s\n", route.Method, route.Path)
	}

	return appCtx
}

func (c *AppContext) SetupCORS() {
	c.App.Use(cors.New(cors.Config{
		AllowHeaders:     "Origin,Content-Type,Accept,Authorization",
		AllowOrigins:     c.Config.App.AllowOrigins,
		AllowCredentials: false,
		AllowMethods:     "GET,POST,HEAD,PUT,DELETE,PATCH,OPTIONS",
	}))
}

func setupRedis(addr string) *redis.Client {
	ctx := context.Background()

	rdb := redis.NewClient(&redis.Options{
		Addr: addr,
	})

	err := rdb.Ping(ctx).Err()
	if err != nil {
		fmt.Printf("Warning: failed to connect to Redis: %v\n", err)
		return nil // return nil to indicate Redis is not connected
	}

	fmt.Println("Testing: Redis connected")

	return rdb
}

func (c *AppContext) SetupAuthorization() {
	c.App.Use(jwtware.New(jwtware.Config{
		ErrorHandler: UnauthorizedHandler,
		SigningKey:   jwtware.SigningKey{Key: []byte("a30a280c0bc249fdb4bdf6ce0cc12b11")},
		SuccessHandler: func(c *fiber.Ctx) error {
			// Extract from parsed JWT (already validated)
			token := c.Locals("user").(*jwt.Token)

			if claims, ok := token.Claims.(jwt.MapClaims); ok {
				rawID, ok := claims["userId"]
				idFloat, isFloat := rawID.(float64)

				if !ok || rawID == nil || !isFloat {
					return fiber.NewError(fiber.StatusUnauthorized, "Invalid or missing userId in token")
				}

				id := int(idFloat)
				c.Locals("user_id", &id)
			}
			return c.Next()
		},
	}))
}

func UnauthorizedHandler(c *fiber.Ctx, err error) error {
	return errs.NewBusinessError(fiber.StatusUnauthorized, constant.Expired, "Invalid or expired JWT", "")
}
