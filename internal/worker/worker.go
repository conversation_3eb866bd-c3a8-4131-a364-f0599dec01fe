package worker

import (
	"context"
	"log"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
)

func StartTaskWorker(rdb *redis.Client) {
	go taskWorker(rdb)
}

// ScheduleTask adds an id to Redis sorted set delayed by delaySeconds
func ScheduleTask(rdb *redis.Client, id string, delaySeconds int) error {
	ctx := context.Background()
	execTime := time.Now().Add(time.Duration(delaySeconds) * time.Second).Unix()

	return rdb.ZAdd(ctx, "delayed_tasks", redis.Z{
		Score:  float64(execTime),
		Member: id,
	}).Err()
}

func taskWorker(rdb *redis.Client) {
	ctx := context.Background()

	for {
		now := time.Now().Unix()

		tasks, err := rdb.ZRangeByScore(ctx, "delayed_tasks", &redis.ZRangeBy{
			Min: "-inf",
			Max: strconv.FormatInt(now, 10),
		}).Result()
		if err != nil {
			log.Println("Worker error:", err)
			time.Sleep(1 * time.Second)
			continue
		}

		for _, message := range tasks {
			// ⚡ Process the task as an alert log
			log.Printf("[ALERT] %s\n", message)

			// Remove it after processing
			_, err := rdb.ZRem(ctx, "delayed_tasks", message).Result()
			if err != nil {
				log.Println("Failed to remove task:", message)
			}
		}

		time.Sleep(1 * time.Second)
	}
}
