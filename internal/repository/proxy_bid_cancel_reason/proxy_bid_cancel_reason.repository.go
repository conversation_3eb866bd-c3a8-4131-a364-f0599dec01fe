package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type proxyBidCancelReasonRepositoryImpl struct {
	DB *gorm.DB
}

type ProxyBidCancelReasonRepository interface {
	FindAllProxyBidCancelReason(req model.PagingRequest) ([]entity.ProxyBidCancelReason, error)
	CountAllProxyBidCancelReason() (int64, error)

	FindProxyBidCancelReasonByID(id int) (*entity.ProxyBidCancelReason, error)

	FindByReason(reason string, excludeId int) (*entity.ProxyBidCancelReason, error)
	InsertProxyBidCancelReason(proxyBidCancelReason *entity.ProxyBidCancelReason) error

	UpdateProxyBidCancelReason(id int, fieldToUpdate map[string]interface{}) (int64, error)

	DeleteProxyBidCancelReason(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewProxyBidCancelReasonRepository(db *gorm.DB) ProxyBidCancelReasonRepository {
	return &proxyBidCancelReasonRepositoryImpl{DB: db}
}
