package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceProviderRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceProviderRepository interface {
	GetProviderByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceProvider, error)

	BulkInsertAdditionalServiceProvider(tx *gorm.DB, entities []entity.AdditionalServiceProvider) error

	PermanentDeleteAdditionalServiceProviderByServiceId(tx *gorm.DB, serviceId int) error

	DeleteAdditionalServiceProviderByServiceId(tx *gorm.DB, serviceId int) error
	GetDB() *gorm.DB
}

func NewAdditionalServiceProviderRepository(db *gorm.DB) AdditionalServiceProviderRepository {
	return &additionalServiceProviderRepositoryImpl{DB: db}
}
