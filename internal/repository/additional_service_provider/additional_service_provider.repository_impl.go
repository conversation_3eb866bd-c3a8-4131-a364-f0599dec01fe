package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceProviderRepositoryImpl) GetProviderByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceProvider, error) {
	var assets []entity.AdditionalServiceProvider
	if err := r.DB.
		Where("additional_service_id IN ?", additionalServiceIds).Find(&assets).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceProvider)
	for _, asset := range assets {
		result[util.Val(asset.AdditionalServiceId)] = append(result[util.Val(asset.AdditionalServiceId)], asset)
	}

	return result, nil
}

func (r *additionalServiceProviderRepositoryImpl) BulkInsertAdditionalServiceProvider(tx *gorm.DB, entities []entity.AdditionalServiceProvider) error {
	if len(entities) == 0 {
		return nil
	}
	if err := tx.Create(&entities).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceProviderRepositoryImpl) PermanentDeleteAdditionalServiceProviderByServiceId(tx *gorm.DB, serviceId int) error {
	if err := tx.Where("additional_service_id = ?", serviceId).
		Unscoped().Delete(&entity.AdditionalServiceProvider{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceProviderRepositoryImpl) DeleteAdditionalServiceProviderByServiceId(tx *gorm.DB, serviceId int) error {
	if err := tx.Where("additional_service_id = ?", serviceId).
		Delete(&entity.AdditionalServiceProvider{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceProviderRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
