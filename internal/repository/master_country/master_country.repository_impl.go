package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCountryRepositoryImpl) buildMasterCountryQuery(req dto.MasterCountryPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCountry{})
	query = util.JoinUsers("master_country")(query)
	//NOTE - apply filters
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_country")

	}
	return query
}

func (r *masterCountryRepositoryImpl) FindMasterCountryWithFilter(req dto.MasterCountryPageReqDto) ([]entity.MasterCountry, error) {
	var results []entity.MasterCountry

	query := r.buildMasterCountryQuery(req)
	query = query.Order("country_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterCountryRepositoryImpl) CountMasterCountryWithFilter(req dto.MasterCountryPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCountryQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCountryRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCountry
	err := r.DB.
		Model(&entity.MasterCountry{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCountryRepositoryImpl) UpdatesMasterCountryFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCountry{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCountryRepositoryImpl) FindMasterCountryAll() ([]entity.MasterCountry, error) {
	var result []entity.MasterCountry
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCountryRepositoryImpl) UpdateMasterCountryAllFields(e *entity.MasterCountry) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCountryRepositoryImpl) InsertMasterCountryList(data []entity.MasterCountry) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCountryRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
