package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type reprintSlipReasonRepositoryImpl struct {
	DB *gorm.DB
}

type ReprintSlipReasonRepository interface {
	FindAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) ([]entity.ReprintSlipReason, error)
	FindReprintSlipReasonById(id int) (entity.ReprintSlipReason, error)
	FindByReason(reason string) (*entity.ReprintSlipReason, error)
	FindDuplicateReasonExcludingId(reason string, excludeID int) (*entity.ReprintSlipReason, error)

	CountAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) (int64, error)

	InsertReprintSlipReason(data entity.ReprintSlipReason) error
	UpdatesReprintSlipReasonFieldsWhere(fields map[string]interface{}, whereC<PERSON><PERSON> string, args ...interface{}) (int64, error)
	DeleteReprintSlipReasonByID(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewReprintSlipReasonRepository(db *gorm.DB) ReprintSlipReasonRepository {
	return &reprintSlipReasonRepositoryImpl{DB: db}
}
