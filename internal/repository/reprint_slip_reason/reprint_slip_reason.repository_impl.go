package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *reprintSlipReasonRepositoryImpl) buildReprintSlipReasonQuery(req dto.ReprintSlipReasonPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.ReprintSlipReason{})
	query = util.JoinUsers("reprint_slip_reason")(query)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "reprint_slip_reason")
	}
	return query
}

func (r *reprintSlipReasonRepositoryImpl) FindAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) ([]entity.ReprintSlipReason, error) {
	var results []entity.ReprintSlipReason

	query := r.buildReprintSlipReasonQuery(req)
	query.Order("updated_date desc ,reason asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *reprintSlipReasonRepositoryImpl) CountAllReprintSlipReason(req dto.ReprintSlipReasonPageReqDto) (int64, error) {
	var count int64
	query := r.buildReprintSlipReasonQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *reprintSlipReasonRepositoryImpl) FindReprintSlipReasonById(id int) (entity.ReprintSlipReason, error) {
	var result entity.ReprintSlipReason
	query := r.DB.Model(&entity.ReprintSlipReason{})
	query = util.JoinUsers("reprint_slip_reason")(query)

	query = query.Where("id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return result, err
	}

	return result, nil
}

func (r *reprintSlipReasonRepositoryImpl) FindByReason(reason string) (*entity.ReprintSlipReason, error) {
	var result entity.ReprintSlipReason
	err := r.DB.Where("reason = ?", reason).First(&result).Error
	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *reprintSlipReasonRepositoryImpl) FindDuplicateReasonExcludingId(reason string, excludeID int) (*entity.ReprintSlipReason, error) {
	var result entity.ReprintSlipReason
	err := r.DB.
		Where("reason = ? AND id != ?", reason, excludeID).
		First(&result).Error

	if err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *reprintSlipReasonRepositoryImpl) InsertReprintSlipReason(data entity.ReprintSlipReason) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *reprintSlipReasonRepositoryImpl) UpdatesReprintSlipReasonFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.ReprintSlipReason{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *reprintSlipReasonRepositoryImpl) DeleteReprintSlipReasonByID(id int) (int64, error) {
	tx := r.DB.Where("id = ?", id).Delete(&entity.ReprintSlipReason{})
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *reprintSlipReasonRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
