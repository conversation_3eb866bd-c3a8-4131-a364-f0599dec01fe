package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterPrefixNameRepositoryImpl) buildMasterPrefixNameQuery(req dto.MasterPrefixNamePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterPrefixName{})
	query = util.JoinUsers("master_prefix_name")(query)
	//NOTE - apply filters
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_prefix_name")

	}
	return query
}

func (r *masterPrefixNameRepositoryImpl) FindMasterPrefixNameWithFilter(req dto.MasterPrefixNamePageReqDto) ([]entity.MasterPrefixName, error) {
	var results []entity.MasterPrefixName

	query := r.buildMasterPrefixNameQuery(req)
	query = query.Order("prefix_name_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterPrefixNameRepositoryImpl) CountMasterPrefixNameWithFilter(req dto.MasterPrefixNamePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterPrefixNameQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterPrefixNameRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterPrefixName
	err := r.DB.
		Model(&entity.MasterPrefixName{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterPrefixNameRepositoryImpl) UpdatesMasterPrefixNameFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterPrefixName{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterPrefixNameRepositoryImpl) FindMasterPrefixNameAll() ([]entity.MasterPrefixName, error) {
	var result []entity.MasterPrefixName
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterPrefixNameRepositoryImpl) UpdateMasterPrefixNameAllFields(e *entity.MasterPrefixName) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPrefixNameRepositoryImpl) InsertMasterPrefixNameList(data []entity.MasterPrefixName) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPrefixNameRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
