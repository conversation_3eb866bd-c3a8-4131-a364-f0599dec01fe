package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterAuctionTypeRepositoryImpl) buildMasterAuctionTypeQuery(req dto.MasterAuctionTypePageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterAuctionType{})
	query = util.JoinUsers("master_auction_type")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_auction_type")

	}
	return query
}

func (r *masterAuctionTypeRepositoryImpl) FindMasterAuctionTypeWithFilter(req dto.MasterAuctionTypePageReqDto) ([]entity.MasterAuctionType, error) {
	var results []entity.MasterAuctionType

	query := r.buildMasterAuctionTypeQuery(req)
	query = query.Order("code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterAuctionTypeRepositoryImpl) FindById(id int) (*entity.MasterAuctionType, error) {
	var result entity.MasterAuctionType
	if err := r.DB.First(&result, id).Error; err != nil {
		return nil, err
	}
	return &result, nil
}

func (r *masterAuctionTypeRepositoryImpl) CountMasterAuctionTypeWithFilter(req dto.MasterAuctionTypePageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterAuctionTypeQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterAuctionTypeRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterAuctionType
	err := r.DB.
		Model(&entity.MasterAuctionType{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterAuctionTypeRepositoryImpl) UpdatesMasterAuctionTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterAuctionType{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterAuctionTypeRepositoryImpl) FindMasterAuctionTypeAll() ([]entity.MasterAuctionType, error) {
	var result []entity.MasterAuctionType
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterAuctionTypeRepositoryImpl) UpdateMasterAuctionTypeAllFields(e *entity.MasterAuctionType) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAuctionTypeRepositoryImpl) InsertMasterAuctionTypeList(data []entity.MasterAuctionType) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAuctionTypeRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
