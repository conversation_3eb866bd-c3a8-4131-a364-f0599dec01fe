package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCustomerSellerOfferRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCustomerSellerOfferRepository interface {
	FindMasterCustomerSellerOfferWithFilter(req dto.MasterCustomerSellerOfferPageReqDto) ([]entity.MasterCustomerSellerOffer, error)
	CountMasterCustomerSellerOfferWithFilter(req dto.MasterCustomerSellerOfferPageReqDto) (int64, error)
	FindMasterCustomerSellerOfferLatestSyncDate() (*time.Time, error)
	UpdatesMasterCustomerSellerOfferFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCustomerSellerOfferAll() ([]entity.MasterCustomerSellerOffer, error)
	UpdateMasterCustomerSellerOfferAllFields(e *entity.MasterCustomerSellerOffer) error
	InsertMasterCustomerSellerOfferList(data []entity.MasterCustomerSellerOffer) error

	GetDB() *gorm.DB
}

func NewMasterCustomerSellerOfferRepository(db *gorm.DB) MasterCustomerSellerOfferRepository {
	return &masterCustomerSellerOfferRepositoryImpl{DB: db}
}
