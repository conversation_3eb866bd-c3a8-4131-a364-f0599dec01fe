package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceCustomerGroupRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceCustomerGroupRepository interface {
	GetCustomerGroupByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceCustomerGroup, error)

	BulkInsertAdditionalServiceCustomerGroup(tx *gorm.DB, entities []entity.AdditionalServiceCustomerGroup) error

	PermanentDeleteAdditionalServiceCustomerGroupByServiceId(tx *gorm.DB, serviceId int) error

	DeleteAdditionalServiceCustomerGroupByServiceId(tx *gorm.DB, serviceId int) error
	GetDB() *gorm.DB
}

func NewAdditionalServiceCustomerGroupRepository(db *gorm.DB) AdditionalServiceCustomerGroupRepository {
	return &additionalServiceCustomerGroupRepositoryImpl{DB: db}
}
