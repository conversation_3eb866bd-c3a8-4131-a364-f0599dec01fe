package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *additionalServiceCustomerGroupRepositoryImpl) GetCustomerGroupByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceCustomerGroup, error) {
	var assets []entity.AdditionalServiceCustomerGroup
	if err := r.DB.
		Preload("CustomerGroup").
		Where("additional_service_id IN ?", additionalServiceIds).Find(&assets).Error; err != nil {
		return nil, err
	}

	result := make(map[int][]entity.AdditionalServiceCustomerGroup)
	for _, asset := range assets {
		result[util.Val(asset.AdditionalServiceId)] = append(result[util.Val(asset.AdditionalServiceId)], asset)
	}

	return result, nil
}

func (r *additionalServiceCustomerGroupRepositoryImpl) BulkInsertAdditionalServiceCustomerGroup(tx *gorm.DB, entities []entity.AdditionalServiceCustomerGroup) error {
	if len(entities) == 0 {
		return nil
	}
	if err := tx.Create(&entities).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceCustomerGroupRepositoryImpl) PermanentDeleteAdditionalServiceCustomerGroupByServiceId(tx *gorm.DB, serviceId int) error {
	if err := tx.Where("additional_service_id = ?", serviceId).
		Unscoped().Delete(&entity.AdditionalServiceCustomerGroup{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceCustomerGroupRepositoryImpl) DeleteAdditionalServiceCustomerGroupByServiceId(tx *gorm.DB, serviceId int) error {
	if err := tx.Where("additional_service_id = ?", serviceId).
		Delete(&entity.AdditionalServiceCustomerGroup{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *additionalServiceCustomerGroupRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
