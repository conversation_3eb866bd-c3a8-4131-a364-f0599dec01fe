package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterReasonRepositoryImpl struct {
	DB *gorm.DB
}

type MasterReasonRepository interface {
	FindMasterReasonWithFilter(req dto.MasterReasonPageReqDto) ([]entity.MasterReason, error)
	CountMasterReasonWithFilter(req dto.MasterReasonPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterReasonFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterReasonAll() ([]entity.MasterReason, error)
	UpdateMasterReasonAllFields(e *entity.MasterReason) error
	InsertMasterReasonList(data []entity.MasterReason) error

	GetDB() *gorm.DB
}

func NewMasterReasonRepository(db *gorm.DB) MasterReasonRepository {
	return &masterReasonRepositoryImpl{DB: db}
}
