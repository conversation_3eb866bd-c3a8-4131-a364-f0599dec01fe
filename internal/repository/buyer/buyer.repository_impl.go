package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *buyerRepositoryImpl) GetById(id int) (entity.Buyer, error) {
	var buyer entity.Buyer
	err := r.DB.First(&buyer, id).Error
	return buyer, err
}

func (r *buyerRepositoryImpl) FindBuyerWithFilter(req dto.MemberSearchReqDto) ([]entity.Buyer, error) {
	var results []entity.Buyer

	query := r.DB.Model(&entity.Buyer{}).Select("buyer.*,CONCAT(first_name, ' ', last_name) AS name,master_prefix_name.description_th AS prefix").
		Joins("JOIN master_prefix_name ON master_prefix_name.id = buyer.prefix_name_id").
		Preload("CustomerGroupForJoin")

	if req.CustomerGroupId != nil {
		query = query.Where("customer_group_id = ?", req.CustomerGroupId)
	}

	if req.Username != nil {
		query = query.Where("username LIKE ?", fmt.Sprintf("%%%s%%", *req.Username))
	}

	if req.BidderId != nil {
		query = query.Where("bidder_id LIKE ?", fmt.Sprintf("%%%s%%", *req.BidderId))
	}

	if req.Name != nil {
		query = query.Where("name LIKE ?", fmt.Sprintf("%%%s%%", *req.Name))
	}

	if req.TaxId != nil {
		query = query.Where("tax_id LIKE ?", fmt.Sprintf("%%%s%%", *req.TaxId))
	}

	if req.SortBy != "" {
		if req.SortBy == "isActive" {
			orderClause := fmt.Sprintf(`
				CASE
					WHEN buyer.is_blacklist = true THEN 1
					WHEN buyer.is_block = true THEN 2
					ELSE 3
				END %s`, req.SortOrder)
			query = query.Order(orderClause)
		} else {
			snakeSort := util.CamelToSnake(req.SortBy)
			query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "buyer")
		}
	}

	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)
	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *buyerRepositoryImpl) UpdateStatus(buyerId int, fields map[string]interface{}) error {
	err := r.DB.Model(&entity.Buyer{}).Where("id = ?", buyerId).Updates(fields).Error
	return err
}

func (r *buyerRepositoryImpl) Create(tx *gorm.DB, buyer *entity.Buyer) error {
	return tx.Create(buyer).Error
}

func (r *buyerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
