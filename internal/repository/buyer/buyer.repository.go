package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type buyerRepositoryImpl struct {
	DB *gorm.DB
}

type BuyerRepository interface {
	GetById(id int) (entity.Buyer, error)
	FindBuyerWithFilter(req dto.MemberSearchReqDto) ([]entity.Buyer, error)
	UpdateStatus(buyerId int, fields map[string]interface{}) error
	GetDB() *gorm.DB
}

func NewBuyerRepository(db *gorm.DB) BuyerRepository {
	return &buyerRepositoryImpl{DB: db}
}
