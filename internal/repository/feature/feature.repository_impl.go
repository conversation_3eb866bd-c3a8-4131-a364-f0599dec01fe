package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *featureRepositoryImpl) FindFeatureAll() (dto.FeatureListDto, error) {
	var result []entity.Feature

	err := r.GetDB().Model(&entity.Feature{}).
		Preload("FeatureActions", func(db *gorm.DB) *gorm.DB {
			return db.Order("sequence ASC")
		}).
		Order("sequence ASC").
		Find(&result).Error

	if err != nil {
		return dto.FeatureListDto{}, fmt.Errorf("failed to query features: %w", err)
	}

	return dto.FeatureListDto{FeatureList: buildFeatureList(result, nil)}, nil
}

func (r *featureRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}

func buildFeatureList(featureList []entity.Feature, parentID *int) []dto.FeatureDto {
	var featureListDto []dto.FeatureDto
	for _, f := range featureList {
		if (f.ParentId == nil && parentID == nil) || (f.ParentId != nil && parentID != nil && *f.ParentId == *parentID) {
			featureActionDtoList := []dto.FeatureActionDto{}
			for _, fa := range f.FeatureActions {
				featureAction := dto.FeatureActionDto{
					Id:         fa.Id,
					FeatureId:  fa.FeatureId,
					ActionCode: fa.ActionCode,
					Sequence:   fa.Sequence,
				}
				featureActionDtoList = append(featureActionDtoList, featureAction)
			}
			dto := dto.FeatureDto{
				Id:                f.Id,
				FeatureName:       f.FeatureName,
				Sequence:          f.Sequence,
				FeatureActionList: featureActionDtoList,
				FeatureList:       buildFeatureList(featureList, &f.Id),
			}
			featureListDto = append(featureListDto, dto)
		}
	}
	return featureListDto
}
