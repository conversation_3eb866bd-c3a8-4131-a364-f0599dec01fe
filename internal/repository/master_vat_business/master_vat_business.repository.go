package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterVatBusinessRepositoryImpl struct {
	DB *gorm.DB
}

type MasterVatBusinessRepository interface {
	FindMasterVatBusinessWithFilter(req dto.MasterVatBusinessPageReqDto) ([]entity.MasterVatBusiness, error)
	CountMasterVatBusinessWithFilter(req dto.MasterVatBusinessPageReqDto) (int64, error)
	FindMasterVatBusinessLatestSyncDate() (*time.Time, error)
	UpdatesMasterVatBusinessFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterVatBusinessAll() ([]entity.MasterVatBusiness, error)
	UpdateMasterVatBusinessAllFields(e *entity.MasterVatBusiness) error
	InsertMasterVatBusinessList(data []entity.MasterVatBusiness) error

	GetDB() *gorm.DB
}

func NewMasterVatBusinessRepository(db *gorm.DB) MasterVatBusinessRepository {
	return &masterVatBusinessRepositoryImpl{DB: db}
}
