package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterVatBusinessRepositoryImpl) buildMasterVatBusinessQuery(req dto.MasterVatBusinessPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterVatBusiness{})
	query = util.JoinUsers("master_asset_type")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_asset_type")
	}
	return query
}

func (r *masterVatBusinessRepositoryImpl) FindMasterVatBusinessWithFilter(req dto.MasterVatBusinessPageReqDto) ([]entity.MasterVatBusiness, error) {
	var results []entity.MasterVatBusiness

	query := r.buildMasterVatBusinessQuery(req)
	query.Order("vat_business_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterVatBusinessRepositoryImpl) CountMasterVatBusinessWithFilter(req dto.MasterVatBusinessPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterVatBusinessQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterVatBusinessRepositoryImpl) FindMasterVatBusinessLatestSyncDate() (*time.Time, error) {
	var result entity.MasterVatBusiness
	err := r.DB.
		Model(&entity.MasterVatBusiness{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterVatBusinessRepositoryImpl) UpdatesMasterVatBusinessFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterVatBusiness{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterVatBusinessRepositoryImpl) FindMasterVatBusinessAll() ([]entity.MasterVatBusiness, error) {
	var result []entity.MasterVatBusiness
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterVatBusinessRepositoryImpl) UpdateMasterVatBusinessAllFields(e *entity.MasterVatBusiness) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVatBusinessRepositoryImpl) InsertMasterVatBusinessList(data []entity.MasterVatBusiness) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterVatBusinessRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
