package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterRegionRepositoryImpl struct {
	DB *gorm.DB
}

type MasterRegionRepository interface {
	FindMasterRegionWithFilter(req dto.MasterRegionPageReqDto) ([]entity.MasterRegion, error)
	CountMasterRegionWithFilter(req dto.MasterRegionPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterRegionFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterRegionAll() ([]entity.MasterRegion, error)
	UpdateMasterRegionAllFields(e *entity.MasterRegion) error
	InsertMasterRegionList(data []entity.MasterRegion) error

	GetDB() *gorm.DB
}

func NewMasterRegionRepository(db *gorm.DB) MasterRegionRepository {
	return &masterRegionRepositoryImpl{DB: db}
}
