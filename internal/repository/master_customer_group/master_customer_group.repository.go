package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCustomerGroupRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCustomerGroupRepository interface {
	FindMasterCustomerGroupWithFilter(req dto.MasterCustomerGroupPageReqDto) ([]entity.MasterCustomerGroup, error)
	CountMasterCustomerGroupWithFilter(req dto.MasterCustomerGroupPageReqDto) (int64, error)
	FindMasterCustomerGroupLatestSyncDate() (*time.Time, error)
	UpdatesMasterCustomerGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCustomerGroupAll() ([]entity.MasterCustomerGroup, error)
	UpdateMasterCustomerGroupAllFields(e *entity.MasterCustomerGroup) error
	InsertMasterCustomerGroupList(data []entity.MasterCustomerGroup) error

	GetDB() *gorm.DB
}

func NewMasterCustomerGroupRepository(db *gorm.DB) MasterCustomerGroupRepository {
	return &masterCustomerGroupRepositoryImpl{DB: db}
}
