package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterAssetLocationFloorRepositoryImpl) buildMasterAssetLocationFloorQuery(req dto.MasterAssetLocationFloorPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterAssetLocationFloor{})
	query = util.JoinUsers("master_asset_location_floor")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_asset_location_floor")
	}
	return query
}

func (r *masterAssetLocationFloorRepositoryImpl) FindMasterAssetLocationFloorWithFilter(req dto.MasterAssetLocationFloorPageReqDto) ([]entity.MasterAssetLocationFloor, error) {
	var results []entity.MasterAssetLocationFloor

	query := r.buildMasterAssetLocationFloorQuery(req)
	query.Order("location_floor_code asc, floor asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterAssetLocationFloorRepositoryImpl) CountMasterAssetLocationFloorWithFilter(req dto.MasterAssetLocationFloorPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterAssetLocationFloorQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterAssetLocationFloorRepositoryImpl) FindMasterAssetLocationFloorLatestSyncDate() (*time.Time, error) {
	var result entity.MasterAssetLocationFloor
	err := r.DB.
		Model(&entity.MasterAssetLocationFloor{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterAssetLocationFloorRepositoryImpl) UpdatesMasterAssetLocationFloorFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterAssetLocationFloor{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterAssetLocationFloorRepositoryImpl) FindMasterAssetLocationFloorAll() ([]entity.MasterAssetLocationFloor, error) {
	var result []entity.MasterAssetLocationFloor
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterAssetLocationFloorRepositoryImpl) UpdateMasterAssetLocationFloorAllFields(e *entity.MasterAssetLocationFloor) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetLocationFloorRepositoryImpl) InsertMasterAssetLocationFloorList(data []entity.MasterAssetLocationFloor) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetLocationFloorRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
