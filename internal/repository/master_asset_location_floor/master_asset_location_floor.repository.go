package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterAssetLocationFloorRepositoryImpl struct {
	DB *gorm.DB
}

type MasterAssetLocationFloorRepository interface {
	FindMasterAssetLocationFloorWithFilter(req dto.MasterAssetLocationFloorPageReqDto) ([]entity.MasterAssetLocationFloor, error)
	CountMasterAssetLocationFloorWithFilter(req dto.MasterAssetLocationFloorPageReqDto) (int64, error)
	FindMasterAssetLocationFloorLatestSyncDate() (*time.Time, error)
	UpdatesMasterAssetLocationFloorFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterAssetLocationFloorAll() ([]entity.MasterAssetLocationFloor, error)
	UpdateMasterAssetLocationFloorAllFields(e *entity.MasterAssetLocationFloor) error
	InsertMasterAssetLocationFloorList(data []entity.MasterAssetLocationFloor) error

	GetDB() *gorm.DB
}

func NewMasterAssetLocationFloorRepository(db *gorm.DB) MasterAssetLocationFloorRepository {
	return &masterAssetLocationFloorRepositoryImpl{DB: db}
}
