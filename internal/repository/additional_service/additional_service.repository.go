package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceRepository interface {
	FindAllAdditionalService(req model.PagingRequest) ([]entity.AdditionalService, error)
	CountAllAdditionalService() (int64, error)

	FindAdditionalServiceByID(id int) (*entity.AdditionalService, error)
	FindDuplicateRecord(req *dto.AdditionalServiceDto, excludedId int) (*entity.AdditionalService, error)
	InsertAdditionalService(tx *gorm.DB, entity *entity.AdditionalService) error

	UpdateAdditionalService(tx *gorm.DB, id int, fieldToUpdate map[string]interface{}) (int64, error)

	DeleteAdditionalService(tx *gorm.DB, id int, actionBy *int) (int64, error)

	GetDB() *gorm.DB
}

func NewAdditionalServiceRepository(db *gorm.DB) AdditionalServiceRepository {
	return &additionalServiceRepositoryImpl{DB: db}
}
