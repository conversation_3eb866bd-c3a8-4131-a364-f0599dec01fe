package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterRegisterTypeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterRegisterTypeRepository interface {
	FindMasterRegisterTypeWithFilter(req dto.MasterRegisterTypePageReqDto) ([]entity.MasterRegisterType, error)
	CountMasterRegisterTypeWithFilter(req dto.MasterRegisterTypePageReqDto) (int64, error)
	FindMasterRegisterTypeLatestSyncDate() (*time.Time, error)
	UpdatesMasterRegisterTypeFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterRegisterTypeAll() ([]entity.MasterRegisterType, error)
	UpdateMasterRegisterTypeAllFields(e *entity.MasterRegisterType) error
	InsertMasterRegisterTypeList(data []entity.MasterRegisterType) error

	GetDB() *gorm.DB
}

func NewMasterRegisterTypeRepository(db *gorm.DB) MasterRegisterTypeRepository {
	return &masterRegisterTypeRepositoryImpl{DB: db}
}
