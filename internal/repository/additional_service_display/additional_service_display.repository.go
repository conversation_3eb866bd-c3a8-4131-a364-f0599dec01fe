package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceDisplayRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceDisplayRepository interface {
	GetDisplayByAdditionalServiceIds(serviceIds ...int) (map[int][]entity.AdditionalServiceDisplay, error)

	BulkInsertAdditionalServiceDisplay(tx *gorm.DB, entities []entity.AdditionalServiceDisplay) error

	PermanentDeleteAdditionalServiceDisplayByServiceId(tx *gorm.DB, serviceId int) error

	DeleteAdditionalServiceDisplayByServiceId(tx *gorm.DB, serviceId int) error
	GetDB() *gorm.DB
}

func NewAdditionalServiceDisplayRepository(db *gorm.DB) AdditionalServiceDisplayRepository {
	return &additionalServiceDisplayRepositoryImpl{DB: db}
}
