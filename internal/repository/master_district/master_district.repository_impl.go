package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterDistrictRepositoryImpl) buildMasterDistrictQuery(req dto.MasterDistrictPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterDistrict{})
	query = util.JoinUsers("master_district")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_district")
	}
	return query
}

func (r *masterDistrictRepositoryImpl) FindMasterDistrictWithFilter(req dto.MasterDistrictPageReqDto) ([]entity.MasterDistrict, error) {
	var results []entity.MasterDistrict

	query := r.buildMasterDistrictQuery(req)
	query.Order("region_code asc, city_code asc, district_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterDistrictRepositoryImpl) CountMasterDistrictWithFilter(req dto.MasterDistrictPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterDistrictQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterDistrictRepositoryImpl) FindMasterDistrictLatestSyncDate() (*time.Time, error) {
	var result entity.MasterDistrict
	err := r.DB.
		Model(&entity.MasterDistrict{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterDistrictRepositoryImpl) UpdatesMasterDistrictFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterDistrict{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterDistrictRepositoryImpl) FindMasterDistrictAll() ([]entity.MasterDistrict, error) {
	var result []entity.MasterDistrict
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterDistrictRepositoryImpl) UpdateMasterDistrictAllFields(e *entity.MasterDistrict) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterDistrictRepositoryImpl) InsertMasterDistrictListWithBatches(data []entity.MasterDistrict) error {
	if err := r.DB.CreateInBatches(&data, 500).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterDistrictRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
