package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterVendorRepositoryImpl struct {
	DB *gorm.DB
}

type MasterVendorRepository interface {
	FindMasterVendorWithFilter(req dto.MasterVendorPageReqDto) ([]entity.MasterVendor, error)
	CountMasterVendorWithFilter(req dto.MasterVendorPageReqDto) (int64, error)
	FindMasterVendorLatestSyncDate() (*time.Time, error)
	UpdatesMasterVendorFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterVendorAll() ([]entity.MasterVendor, error)
	UpdateMasterVendorAllFields(e *entity.MasterVendor) error
	InsertMasterVendorList(data []entity.MasterVendor) error

	GetDB() *gorm.DB
}

func NewMasterVendorRepository(db *gorm.DB) MasterVendorRepository {
	return &masterVendorRepositoryImpl{DB: db}
}
