package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterHolidayRepositoryImpl struct {
	DB *gorm.DB
}

type MasterHolidayRepository interface {
	FindMasterHolidayWithFilter(req dto.MasterHolidayPageReqDto) ([]entity.MasterHoliday, error)
	CountMasterHolidayWithFilter(req dto.MasterHolidayPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterHolidayFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterHolidayAll() ([]entity.MasterHoliday, error)
	UpdateMasterHolidayAllFields(e *entity.MasterHoliday) error
	InsertMasterHolidayList(data []entity.MasterHoliday) error

	GetDB() *gorm.DB
}

func NewMasterHolidayRepository(db *gorm.DB) MasterHolidayRepository {
	return &masterHolidayRepositoryImpl{DB: db}
}
