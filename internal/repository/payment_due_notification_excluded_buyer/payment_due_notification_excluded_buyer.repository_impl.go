package repository

import (
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) GetTopExcludedBuyersByNotificationIds(ids []int, topN int) (map[int][]entity.PaymentDueNotificationExcludedBuyer, error) {
	result := []entity.PaymentDueNotificationExcludedBuyer{}

	subQuery := fmt.Sprintf(`
		SELECT * FROM (
			SELECT *,
			RANK() OVER (PARTITION BY payment_due_notification_id ORDER BY id ASC) AS rank
			FROM payment_due_notification_buyer
		) AS ranked
		WHERE ranked.rank <= %d
	`, topN)

	if err := r.DB.Table("(?) as top_buyers", gorm.Expr(subQuery)).
		Preload("Buyer").
		Where("payment_due_notification_id IN ?", ids).Find(&result).Error; err != nil {
		return nil, err
	}

	buyersMap := make(map[int][]entity.PaymentDueNotificationExcludedBuyer)
	for _, b := range result {
		buyersMap[b.PaymentDueNotificationId] = append(buyersMap[b.PaymentDueNotificationId], b)
	}

	return buyersMap, nil
}

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) GetExcludedBuyersByNotificationId(id int) ([]entity.PaymentDueNotificationExcludedBuyer, error) {
	var excludedBuyers []entity.PaymentDueNotificationExcludedBuyer

	err := r.DB.
		Preload("Buyer").
		Where("payment_due_notification_id = ?", id).
		Find(&excludedBuyers).Error
	if err != nil {
		return nil, err
	}

	return excludedBuyers, nil
}

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) BulkInsertExcludedBuyers(excludedBuyers []entity.PaymentDueNotificationExcludedBuyer) error {
	if len(excludedBuyers) == 0 {
		return nil
	}

	if err := r.DB.Create(&excludedBuyers).Error; err != nil {
		return err
	}

	return nil
}

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) PermanentDeleteExcludedBuyers(paymentDueNotificationId int) error {
	if err := r.DB.Where("payment_due_notification_id = ?", paymentDueNotificationId).
		Unscoped().Delete(&entity.PaymentDueNotificationExcludedBuyer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) DeleteExcludedBuyerNotificationId(paymentDueNotificationId int) error {
	if err := r.DB.Where("payment_due_notification_id = ?", paymentDueNotificationId).
		Delete(&entity.PaymentDueNotificationExcludedBuyer{}).Error; err != nil {
		return err
	}
	return nil
}

func (r *paymentDueNotificationExcludedBuyerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
