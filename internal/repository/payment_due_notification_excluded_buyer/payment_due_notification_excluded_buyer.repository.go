package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type paymentDueNotificationExcludedBuyerRepositoryImpl struct {
	DB *gorm.DB
}

type PaymentDueNotificationExcludedBuyerRepository interface {
	GetTopExcludedBuyersByNotificationIds(ids []int, topN int) (map[int][]entity.PaymentDueNotificationExcludedBuyer, error)
	GetExcludedBuyersByNotificationId(id int) ([]entity.PaymentDueNotificationExcludedBuyer, error)

	BulkInsertExcludedBuyers(excludedBuyers []entity.PaymentDueNotificationExcludedBuyer) error
	PermanentDeleteExcludedBuyers(paymentDueNotificationId int) error

	DeleteExcludedBuyerNotificationId(paymentDueNotificationId int) error
	GetDB() *gorm.DB
}

func NewPaymentDueNotificationExcludedBuyerRepository(db *gorm.DB) PaymentDueNotificationExcludedBuyerRepository {
	return &paymentDueNotificationExcludedBuyerRepositoryImpl{DB: db}
}
