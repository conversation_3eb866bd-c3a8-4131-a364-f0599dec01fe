package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceAssetRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceAssetRepository interface {
	GetAssetByAdditionalServiceIds(additionalServiceIds ...int) (map[int][]entity.AdditionalServiceAsset, error)

	BulkInsertAdditionalServiceAsset(tx *gorm.DB, assets []entity.AdditionalServiceAsset) error

	PermanentDeleteAdditionalServiceAssetByServiceId(tx *gorm.DB, serviceId int) error

	DeleteAdditionalServiceAssetByServiceId(tx *gorm.DB, serviceId int) error
	GetDB() *gorm.DB
}

func NewAdditionalServiceAssetRepository(db *gorm.DB) AdditionalServiceAssetRepository {
	return &additionalServiceAssetRepositoryImpl{DB: db}
}
