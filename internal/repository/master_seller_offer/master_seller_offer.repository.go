package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterSellerOfferRepositoryImpl struct {
	DB *gorm.DB
}

type MasterSellerOfferRepository interface {
	FindMasterSellerOfferWithFilter(req dto.MasterSellerOfferPageReqDto) ([]entity.MasterSellerOffer, error)
	CountMasterSellerOfferWithFilter(req dto.MasterSellerOfferPageReqDto) (int64, error)
	FindMasterSellerOfferLatestSyncDate() (*time.Time, error)
	UpdatesMasterSellerOfferFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterSellerOfferAll() ([]entity.MasterSellerOffer, error)
	UpdateMasterSellerOfferAllFields(e *entity.MasterSellerOffer) error
	InsertMasterSellerOfferList(data []entity.MasterSellerOffer) error

	GetDB() *gorm.DB
}

func NewMasterSellerOfferRepository(db *gorm.DB) MasterSellerOfferRepository {
	return &masterSellerOfferRepositoryImpl{DB: db}
}
