package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterPaymentMethodRepositoryImpl struct {
	DB *gorm.DB
}

type MasterPaymentMethodRepository interface {
	FindMasterPaymentMethodWithFilter(req dto.MasterPaymentMethodPageReqDto) ([]entity.MasterPaymentMethod, error)
	CountMasterPaymentMethodWithFilter(req dto.MasterPaymentMethodPageReqDto) (int64, error)
	FindLatestSyncDate() (*time.Time, error)

	UpdatesMasterPaymentMethodFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)

	FindMasterPaymentMethodAll() ([]entity.MasterPaymentMethod, error)
	UpdateMasterPaymentMethodAllFields(e *entity.MasterPaymentMethod) error
	InsertMasterPaymentMethodList(data []entity.MasterPaymentMethod) error

	GetDB() *gorm.DB
}

func NewMasterPaymentMethodRepository(db *gorm.DB) MasterPaymentMethodRepository {
	return &masterPaymentMethodRepositoryImpl{DB: db}
}
