package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterPaymentMethodRepositoryImpl) buildMasterPaymentMethodQuery(req dto.MasterPaymentMethodPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterPaymentMethod{})
	query = util.JoinUsers("master_payment_method")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_payment_method")

	}
	return query
}

func (r *masterPaymentMethodRepositoryImpl) FindMasterPaymentMethodWithFilter(req dto.MasterPaymentMethodPageReqDto) ([]entity.MasterPaymentMethod, error) {
	var results []entity.MasterPaymentMethod

	query := r.buildMasterPaymentMethodQuery(req)
	query = query.Order("payment_method_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterPaymentMethodRepositoryImpl) CountMasterPaymentMethodWithFilter(req dto.MasterPaymentMethodPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterPaymentMethodQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterPaymentMethodRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
	var result entity.MasterPaymentMethod
	err := r.DB.
		Model(&entity.MasterPaymentMethod{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterPaymentMethodRepositoryImpl) UpdatesMasterPaymentMethodFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterPaymentMethod{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterPaymentMethodRepositoryImpl) FindMasterPaymentMethodAll() ([]entity.MasterPaymentMethod, error) {
	var result []entity.MasterPaymentMethod
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterPaymentMethodRepositoryImpl) UpdateMasterPaymentMethodAllFields(e *entity.MasterPaymentMethod) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPaymentMethodRepositoryImpl) InsertMasterPaymentMethodList(data []entity.MasterPaymentMethod) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterPaymentMethodRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
