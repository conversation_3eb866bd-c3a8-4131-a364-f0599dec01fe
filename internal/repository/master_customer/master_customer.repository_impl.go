package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterCustomerRepositoryImpl) buildMasterCustomerQuery(req dto.MasterCustomerPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterCustomer{})
	query = util.JoinUsers("master_customer")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_customer")
	}
	return query
}

func (r *masterCustomerRepositoryImpl) FindMasterCustomerWithFilter(req dto.MasterCustomerPageReqDto) ([]entity.MasterCustomer, error) {
	var results []entity.MasterCustomer

	query := r.buildMasterCustomerQuery(req)
	query.Order("customer_no asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterCustomerRepositoryImpl) CountMasterCustomerWithFilter(req dto.MasterCustomerPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterCustomerQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterCustomerRepositoryImpl) FindMasterCustomerLatestSyncDate() (*time.Time, error) {
	var result entity.MasterCustomer
	err := r.DB.
		Model(&entity.MasterCustomer{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterCustomerRepositoryImpl) UpdatesMasterCustomerFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterCustomer{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterCustomerRepositoryImpl) FindMasterCustomerAll() ([]entity.MasterCustomer, error) {
	var result []entity.MasterCustomer
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterCustomerRepositoryImpl) UpdateMasterCustomerAllFields(e *entity.MasterCustomer) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerRepositoryImpl) InsertMasterCustomerListWithBatches(data []entity.MasterCustomer) error {
	if err := r.DB.CreateInBatches(&data, 500).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterCustomerRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
