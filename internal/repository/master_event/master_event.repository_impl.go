package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterEventRepositoryImpl) buildMasterEventQuery(req dto.MasterEventPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterEvent{})
	query = util.JoinUsers("master_event")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_event")
	}
	return query
}

func (r *masterEventRepositoryImpl) FindMasterEventWithFilter(req dto.MasterEventPageReqDto) ([]entity.MasterEvent, error) {
	var results []entity.MasterEvent

	query := r.buildMasterEventQuery(req)
	query.Order("event_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterEventRepositoryImpl) CountMasterEventWithFilter(req dto.MasterEventPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterEventQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterEventRepositoryImpl) FindMasterEventLatestSyncDate() (*time.Time, error) {
	var result entity.MasterEvent
	err := r.DB.
		Model(&entity.MasterEvent{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterEventRepositoryImpl) UpdatesMasterEventFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterEvent{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterEventRepositoryImpl) FindMasterEventAll() ([]entity.MasterEvent, error) {
	var result []entity.MasterEvent
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterEventRepositoryImpl) UpdateMasterEventAllFields(e *entity.MasterEvent) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterEventRepositoryImpl) InsertMasterEventList(data []entity.MasterEvent) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterEventRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
