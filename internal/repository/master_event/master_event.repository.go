package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterEventRepositoryImpl struct {
	DB *gorm.DB
}

type MasterEventRepository interface {
	FindMasterEventWithFilter(req dto.MasterEventPageReqDto) ([]entity.MasterEvent, error)
	CountMasterEventWithFilter(req dto.MasterEventPageReqDto) (int64, error)
	FindMasterEventLatestSyncDate() (*time.Time, error)
	UpdatesMasterEventFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterEventAll() ([]entity.MasterEvent, error)
	UpdateMasterEventAllFields(e *entity.MasterEvent) error
	InsertMasterEventList(data []entity.MasterEvent) error

	GetDB() *gorm.DB
}

func NewMasterEventRepository(db *gorm.DB) MasterEventRepository {
	return &masterEventRepositoryImpl{DB: db}
}
