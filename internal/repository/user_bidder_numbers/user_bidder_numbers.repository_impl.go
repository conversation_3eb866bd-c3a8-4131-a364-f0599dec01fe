package repository

import (
	"content-service/internal/model/entity"
)

func (r *userBidderNumbersRepositoryImpl) FindUserBidderNumberByBidderNumber(bidderNumberId string) (*entity.UserBidderNumbers, error) {
	var userBidderNumbers *entity.UserBidderNumbers
	err := r.DB.Table("user_bidder_numbers").
		Select(
			`
			user_bidder_numbers.id,
			user_bidder_numbers.user_id,
			user_bidder_numbers.bidder_number,
			b.first_name as buyer_first_name,
			b.middle_name as buyer_middle_name,
			b.last_name as buyer_last_name,
			mpn.description_th as prefix_name

			`,
		).
		Joins("LEFT JOIN buyer b ON b.user_id = user_bidder_numbers.user_id").
		Joins("LEFT JOIN master_prefix_name mpn ON mpn.id = b.prefix_name_id").
		Where("bidder_number = ?", bidderNumberId).First(&userBidderNumbers).Error

	return userBidderNumbers, err
}
