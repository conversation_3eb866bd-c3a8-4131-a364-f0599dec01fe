package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type userBidderNumbersRepositoryImpl struct {
	DB *gorm.DB
}

type UserBidderNumbersRepository interface {
	FindUserBidderNumberByBidderNumber(bidderNumberId string) (*entity.UserBidderNumbers, error)
}

func NewUserBidderNumbersRepository(db *gorm.DB) UserBidderNumbersRepository {
	return &userBidderNumbersRepositoryImpl{DB: db}
}
