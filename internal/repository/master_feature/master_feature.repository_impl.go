package repository

import (
	"gorm.io/gorm"
)

// func (r *masterFeatureRepositoryImpl) buildMasterFeatureQuery(req dto.MasterFeaturePageReqDto) *gorm.DB {
// 	query := r.DB.Model(&entity.MasterFeature{})
// 	query = util.JoinUsers("master_holiday")(query)
// 	//NOTE - apply filters
// 	query = util.ApplyFiltersFromStruct(query, req)
// 	if req.Date != nil {
// 		query = query.Where("date = ?", req.Date)
// 	}

// 	//NOTE - sorting
// 	if req.SortBy != "" {
// 		snakeSort := util.CamelToSnake(req.SortBy)
// 		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_holiday")

// 	}
// 	return query
// }

// func (r *masterFeatureRepositoryImpl) FindMasterFeatureWithFilter(req dto.MasterFeaturePageReqDto) ([]entity.MasterFeature, error) {
// 	var results []entity.MasterFeature

// 	query := r.buildMasterFeatureQuery(req)
// 	query = query.Order("date desc")

// 	//NOTE - pagination
// 	offset := (req.PageNumber - 1) * req.PageLimit
// 	query = query.Offset(offset).Limit(req.PageLimit)

// 	if err := query.Find(&results).Error; err != nil {
// 		return nil, err
// 	}

// 	return results, nil
// }

// func (r *masterFeatureRepositoryImpl) CountMasterFeatureWithFilter(req dto.MasterFeaturePageReqDto) (int64, error) {
// 	var count int64
// 	query := r.buildMasterFeatureQuery(req)
// 	if err := query.Count(&count).Error; err != nil {
// 		return 0, err
// 	}
// 	return count, nil
// }

// func (r *masterFeatureRepositoryImpl) FindLatestSyncDate() (*time.Time, error) {
// 	var result entity.MasterFeature
// 	err := r.DB.
// 		Model(&entity.MasterFeature{}).
// 		Select("latest_sync_date").
// 		Where("latest_sync_date IS NOT NULL").
// 		Order("latest_sync_date desc").
// 		Limit(1).
// 		First(&result).Error

// 	if err != nil {
// 		if errors.Is(err, gorm.ErrRecordNotFound) {
// 			return nil, nil
// 		}
// 		return nil, err
// 	}

// 	return result.LatestSyncDate, nil
// }

// func (r *masterFeatureRepositoryImpl) UpdatesMasterFeatureFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
// 	tx := r.DB.Model(&entity.MasterFeature{}).Where(whereClause, args...).Updates(fields)
// 	if tx.Error != nil {
// 		return 0, tx.Error
// 	}
// 	return tx.RowsAffected, nil
// }

// func (r *masterFeatureRepositoryImpl) FindMasterFeatureAll() ([]entity.MasterFeature, error) {
// 	var result []entity.MasterFeature
// 	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
// 		return nil, err
// 	}
// 	return result, nil
// }

// func (r *masterFeatureRepositoryImpl) UpdateMasterFeatureAllFields(e *entity.MasterFeature) error {
// 	if err := r.DB.Save(e).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

// func (r *masterFeatureRepositoryImpl) InsertMasterFeatureList(data []entity.MasterFeature) error {
// 	if err := r.DB.Create(&data).Error; err != nil {
// 		return err
// 	}
// 	return nil
// }

func (r *masterFeatureRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
