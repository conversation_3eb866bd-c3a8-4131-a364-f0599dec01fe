package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *floorSpecialRemarkRepositoryImpl) buildFloorSpecialRemarkQuery(req *dto.FloorSpecialRemarkSearchReqDto) *gorm.DB {
	query := r.DB.Model(&entity.FloorSpecialRemark{})
	query = util.JoinUsers("floor_special_remark")(query)

	if req != nil {
		query = util.ApplyFiltersFromStruct(query, req)
	}

	return query
}

func (r *floorSpecialRemarkRepositoryImpl) FindFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) ([]entity.FloorSpecialRemark, error) {
	var results []entity.FloorSpecialRemark

	query := r.buildFloorSpecialRemarkQuery(util.Ptr(req))
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "floor_special_remark")
	}

	//NOTE - default order
	query.Order("updated_date desc ,remark asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *floorSpecialRemarkRepositoryImpl) CountFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) (int64, error) {
	var count int64
	query := r.buildFloorSpecialRemarkQuery(util.Ptr(req))
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *floorSpecialRemarkRepositoryImpl) FindFloorSpecialRemarkByID(id int) (*entity.FloorSpecialRemark, error) {
	var result entity.FloorSpecialRemark

	query := r.buildFloorSpecialRemarkQuery(nil)
	query = query.Where("id = ?", id)

	if err := query.First(&result).Error; err != nil {
		return nil, err
	}

	return &result, nil
}

func (r *floorSpecialRemarkRepositoryImpl) FindByRemark(remark string, excludeID int) (*entity.FloorSpecialRemark, error) {
	var results entity.FloorSpecialRemark

	query := r.DB.Where("remark = ? AND id != ?", remark, excludeID)

	if err := query.First(&results).Error; err != nil {
		return nil, err
	}

	return &results, nil
}

func (r *floorSpecialRemarkRepositoryImpl) InsertFloorSpecialRemark(floorSpecialRemark *entity.FloorSpecialRemark) error {
	if err := r.DB.Create(floorSpecialRemark).Error; err != nil {
		return err
	}
	return nil
}

func (r *floorSpecialRemarkRepositoryImpl) UpdateFloorSpecialRemark(id int, fieldToUpdate map[string]interface{}) (int64, error) {
	result := r.DB.Model(&entity.FloorSpecialRemark{}).
		Where("id = ?", id).
		Updates(fieldToUpdate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *floorSpecialRemarkRepositoryImpl) DeleteFloorSpecialRemark(id int) (int64, error) {
	result := r.DB.Where("id = ?", id).Delete(&entity.FloorSpecialRemark{})

	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (r *floorSpecialRemarkRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
