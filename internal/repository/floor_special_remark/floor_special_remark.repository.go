package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type floorSpecialRemarkRepositoryImpl struct {
	DB *gorm.DB
}

type FloorSpecialRemarkRepository interface {
	FindFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) ([]entity.FloorSpecialRemark, error)
	CountFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) (int64, error)

	FindFloorSpecialRemarkByID(id int) (*entity.FloorSpecialRemark, error)

	FindByRemark(reason string, excludeId int) (*entity.FloorSpecialRemark, error)
	InsertFloorSpecialRemark(floorSpecialRemark *entity.FloorSpecialRemark) error

	UpdateFloorSpecialRemark(id int, fieldToUpdate map[string]interface{}) (int64, error)

	DeleteFloorSpecialRemark(id int) (int64, error)

	GetDB() *gorm.DB
}

func NewFloorSpecialRemarkRepository(db *gorm.DB) FloorSpecialRemarkRepository {
	return &floorSpecialRemarkRepositoryImpl{DB: db}
}
