package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterRegisterTypeCarRepositoryImpl) buildMasterRegisterTypeCarQuery(req dto.MasterRegisterTypeCarPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterRegisterTypeCar{})
	query = util.JoinUsers("master_register_type_car")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_register_type_car")
	}
	return query
}

func (r *masterRegisterTypeCarRepositoryImpl) FindMasterRegisterTypeCarWithFilter(req dto.MasterRegisterTypeCarPageReqDto) ([]entity.MasterRegisterTypeCar, error) {
	var results []entity.MasterRegisterTypeCar

	query := r.buildMasterRegisterTypeCarQuery(req)
	query.Order("asset_register_type_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterRegisterTypeCarRepositoryImpl) CountMasterRegisterTypeCarWithFilter(req dto.MasterRegisterTypeCarPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterRegisterTypeCarQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterRegisterTypeCarRepositoryImpl) FindMasterRegisterTypeCarLatestSyncDate() (*time.Time, error) {
	var result entity.MasterRegisterTypeCar
	err := r.DB.
		Model(&entity.MasterRegisterTypeCar{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterRegisterTypeCarRepositoryImpl) UpdatesMasterRegisterTypeCarFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterRegisterTypeCar{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterRegisterTypeCarRepositoryImpl) FindMasterRegisterTypeCarAll() ([]entity.MasterRegisterTypeCar, error) {
	var result []entity.MasterRegisterTypeCar
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterRegisterTypeCarRepositoryImpl) UpdateMasterRegisterTypeCarAllFields(e *entity.MasterRegisterTypeCar) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegisterTypeCarRepositoryImpl) InsertMasterRegisterTypeCarList(data []entity.MasterRegisterTypeCar) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterRegisterTypeCarRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
