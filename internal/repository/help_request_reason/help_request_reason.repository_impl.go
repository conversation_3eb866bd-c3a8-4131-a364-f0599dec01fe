package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *helpRequestReasonRepositoryImpl) FindAllHelpRequestReason() ([]entity.HelpRequestReason, error) {
	var results []entity.HelpRequestReason
	if err := r.DB.Model(&entity.HelpRequestReason{}).Find(&results).Error; err != nil {
		return nil, err
	}
	return results, nil
}

func (r *helpRequestReasonRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
