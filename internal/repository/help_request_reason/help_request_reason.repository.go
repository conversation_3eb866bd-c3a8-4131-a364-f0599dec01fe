package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type helpRequestReasonRepositoryImpl struct {
	DB *gorm.DB
}

type HelpRequestReasonRepository interface {
	FindAllHelpRequestReason() ([]entity.HelpRequestReason, error)

	GetDB() *gorm.DB
}

func NewHelpRequestReasonRepository(db *gorm.DB) HelpRequestReasonRepository {
	return &helpRequestReasonRepositoryImpl{DB: db}
}
