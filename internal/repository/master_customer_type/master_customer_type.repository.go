package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterCustomerTypeRepositoryImpl struct {
	DB *gorm.DB
}

type MasterCustomerTypeRepository interface {
	FindMasterCustomerTypeWithFilter(req dto.MasterCustomerTypePageReqDto) ([]entity.MasterCustomerType, error)
	CountMasterCustomerTypeWithFilter(req dto.MasterCustomerTypePageReqDto) (int64, error)
	FindMasterCustomerTypeLatestSyncDate() (*time.Time, error)
	UpdatesMasterCustomerTypeFieldsWhere(field map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterCustomerTypeAll() ([]entity.MasterCustomerType, error)
	UpdateMasterCustomerTypeAllFields(e *entity.MasterCustomerType) error
	InsertMasterCustomerTypeList(data []entity.MasterCustomerType) error

	GetDB() *gorm.DB
}

func NewMasterCustomerTypeRepository(db *gorm.DB) MasterCustomerTypeRepository {
	return &masterCustomerTypeRepositoryImpl{DB: db}
}
