package repository

import (
	"backend-common-lib/model"
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type additionalServiceInterestRepositoryImpl struct {
	DB *gorm.DB
}

type AdditionalServiceInterestRepository interface {
	FindAllAdditionalServiceInterest(req model.PagingRequest) ([]entity.AdditionalServiceInterest, error)
	CountAllAdditionalServiceInterest() (int64, error)

	GetDB() *gorm.DB
}

func NewAdditionalServiceInterestRepository(db *gorm.DB) AdditionalServiceInterestRepository {
	return &additionalServiceInterestRepositoryImpl{DB: db}
}
