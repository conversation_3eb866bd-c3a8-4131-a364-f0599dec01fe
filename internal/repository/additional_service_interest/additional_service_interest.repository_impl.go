package repository

import (
	"backend-common-lib/model"
	"backend-common-lib/util"
	contentConst "content-service/constant"
	"content-service/internal/model/entity"
	"fmt"

	"gorm.io/gorm"
)

func (r *additionalServiceInterestRepositoryImpl) buildAdditionalServiceQuery() *gorm.DB {
	query := r.DB.Model(&entity.AdditionalServiceInterest{}).
		Select(`
		additional_service_interest.*,
		additional_service.service_name,
		buyer.first_name || ' ' || COALESCE(buyer.middle_name, '') || ' ' || buyer.last_name AS buyer_name,
		buyer.phone_number,
		buyer.bidder_id,
		buyer.email
	`).
		Joins("LEFT JOIN additional_service ON additional_service.id = additional_service_interest.additional_service_id").
		Joins("LEFT JOIN buyer ON buyer.user_id = additional_service_interest.created_by").
		Preload("UpdatedUser")

	return query
}

func (r *additionalServiceInterestRepositoryImpl) FindAllAdditionalServiceInterest(req model.PagingRequest) ([]entity.AdditionalServiceInterest, error) {
	var results []entity.AdditionalServiceInterest

	query := r.buildAdditionalServiceQuery()
	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplySortFromMapField(query, snakeSort, req.SortOrder, "additional_service_interest", contentConst.SortingAdditionalServiceInterest, true)
	}

	//NOTE - default order
	query.Order(fmt.Sprintf("%s %s, %s %s", "updated_date", "asc", "id", "asc"))

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *additionalServiceInterestRepositoryImpl) CountAllAdditionalServiceInterest() (int64, error) {
	var count int64
	query := r.buildAdditionalServiceQuery()
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *additionalServiceInterestRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
