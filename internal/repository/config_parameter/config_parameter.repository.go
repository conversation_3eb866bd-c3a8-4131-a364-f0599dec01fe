package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

type configParameterRepositoryImpl struct {
	DB *gorm.DB
}

type ConfigParameterRepository interface {
	FindConfigParameterByModuleAndName(module string, name string) (*entity.ConfigParameter, error)
	FindConfigParametersByModuleAndNames(module string, names []string) ([]entity.ConfigParameter, error)
	GetDB() *gorm.DB
}

func NewConfigParameterRepository(db *gorm.DB) ConfigParameterRepository {
	return &configParameterRepositoryImpl{DB: db}
}
