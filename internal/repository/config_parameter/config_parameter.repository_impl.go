package repository

import (
	"content-service/internal/model/entity"

	"gorm.io/gorm"
)

func (r *configParameterRepositoryImpl) FindConfigParameterByModuleAndName(module string, name string) (*entity.ConfigParameter, error) {
	var configParameter entity.ConfigParameter
	if err := r.DB.Where("parameter_module = ? AND parameter_name = ?", module, name).First(&configParameter).Error; err != nil {
		return nil, err
	}
	return &configParameter, nil
}

func (r *configParameterRepositoryImpl) FindConfigParametersByModuleAndNames(module string, names []string) ([]entity.ConfigParameter, error) {
	var configParameters []entity.ConfigParameter
	if err := r.DB.Where("parameter_module = ? AND parameter_name IN ?", module, names).Find(&configParameters).Error; err != nil {
		return nil, err
	}
	return configParameters, nil
}

func (r *configParameterRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
