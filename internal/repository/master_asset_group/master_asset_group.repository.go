package repository

import (
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"time"

	"gorm.io/gorm"
)

type masterAssetGroupRepositoryImpl struct {
	DB *gorm.DB
}

type MasterAssetGroupRepository interface {
	FindMasterAssetGroupWithFilter(req dto.MasterAssetGroupPageReqDto) ([]entity.MasterAssetGroup, error)
	CountMasterAssetGroupWithFilter(req dto.MasterAssetGroupPageReqDto) (int64, error)
	FindMasterAssetGroupLatestSyncDate() (*time.Time, error)
	UpdatesMasterAssetGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error)
	FindMasterAssetGroupAll() ([]entity.MasterAssetGroup, error)
	UpdateMasterAssetGroupAllFields(e *entity.MasterAssetGroup) error
	InsertMasterAssetGroupList(data []entity.MasterAssetGroup) error

	GetDB() *gorm.DB
}

func NewMasterAssetGroupRepository(db *gorm.DB) MasterAssetGroupRepository {
	return &masterAssetGroupRepositoryImpl{DB: db}
}
