package repository

import (
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"time"

	"gorm.io/gorm"
)

func (r *masterAssetGroupRepositoryImpl) buildMasterAssetGroupQuery(req dto.MasterAssetGroupPageReqDto) *gorm.DB {
	query := r.DB.Model(&entity.MasterAssetGroup{})
	query = util.JoinUsers("master_asset_group")(query)
	query = util.ApplyFiltersFromStruct(query, req)

	//NOTE - sorting
	if req.SortBy != "" {
		snakeSort := util.CamelToSnake(req.SortBy)
		query = util.ApplyEmployeeSort(query, snakeSort, req.SortOrder, "master_asset_group")
	}
	return query
}

func (r *masterAssetGroupRepositoryImpl) FindMasterAssetGroupWithFilter(req dto.MasterAssetGroupPageReqDto) ([]entity.MasterAssetGroup, error) {
	var results []entity.MasterAssetGroup

	query := r.buildMasterAssetGroupQuery(req)
	query.Order("asset_group_code asc")

	//NOTE - pagination
	offset := (req.PageNumber - 1) * req.PageLimit
	query = query.Offset(offset).Limit(req.PageLimit)

	if err := query.Find(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *masterAssetGroupRepositoryImpl) CountMasterAssetGroupWithFilter(req dto.MasterAssetGroupPageReqDto) (int64, error) {
	var count int64
	query := r.buildMasterAssetGroupQuery(req)
	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (r *masterAssetGroupRepositoryImpl) FindMasterAssetGroupLatestSyncDate() (*time.Time, error) {
	var result entity.MasterAssetGroup
	err := r.DB.
		Model(&entity.MasterAssetGroup{}).
		Select("latest_sync_date").
		Where("latest_sync_date IS NOT NULL").
		Order("latest_sync_date desc").
		Limit(1).
		First(&result).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	return result.LatestSyncDate, nil
}

func (r *masterAssetGroupRepositoryImpl) UpdatesMasterAssetGroupFieldsWhere(fields map[string]interface{}, whereClause string, args ...interface{}) (int64, error) {
	tx := r.DB.Model(&entity.MasterAssetGroup{}).Where(whereClause, args...).Updates(fields)
	if tx.Error != nil {
		return 0, tx.Error
	}
	return tx.RowsAffected, nil
}

func (r *masterAssetGroupRepositoryImpl) FindMasterAssetGroupAll() ([]entity.MasterAssetGroup, error) {
	var result []entity.MasterAssetGroup
	if err := r.DB.Where("1 = ?", 1).Find(&result).Error; err != nil {
		return nil, err
	}
	return result, nil
}

func (r *masterAssetGroupRepositoryImpl) UpdateMasterAssetGroupAllFields(e *entity.MasterAssetGroup) error {
	if err := r.DB.Save(e).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetGroupRepositoryImpl) InsertMasterAssetGroupList(data []entity.MasterAssetGroup) error {
	if err := r.DB.Create(&data).Error; err != nil {
		return err
	}
	return nil
}

func (r *masterAssetGroupRepositoryImpl) GetDB() *gorm.DB {
	return r.DB
}
