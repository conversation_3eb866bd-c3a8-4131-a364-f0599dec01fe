package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_bank"
)

type masterBankService struct {
	Repo repository.MasterBankRepository
}

type MasterBankService interface {
	SearchMasterBankFilter(req dto.MasterBankPageReqDto) (dto.MasterBankPageRespDto[dto.MasterBankDto], error)
	UpdateMasterBankStatus(req dto.MasterBankUpdateReqDto) error
	SyncMasterBankFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterBankService(repo repository.MasterBankRepository) MasterBankService {
	return &masterBankService{Repo: repo}
}
