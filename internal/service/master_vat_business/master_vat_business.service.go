package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_vat_business"
)

type masterVatBusinessService struct {
	Repo repository.MasterVatBusinessRepository
}

type MasterVatBusinessService interface {
	SearchMasterVatBusinessFilter(req dto.MasterVatBusinessPageReqDto) (dto.MasterVatBusinessPageRespDto[dto.MasterVatBusinessDto], error)
	UpdateMasterVatBusinessStatus(req dto.MasterVatBusinessUpdateReqDto) error
	SyncMasterVatBusinessFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterVatBusinessService(repo repository.MasterVatBusinessRepository) MasterVatBusinessService {
	return &masterVatBusinessService{Repo: repo}
}
