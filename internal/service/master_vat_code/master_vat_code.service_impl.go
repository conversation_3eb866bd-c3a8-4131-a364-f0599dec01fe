package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_vat_code"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterVatCodeService) SearchMasterVatCodeFilter(req dto.MasterVatCodePageReqDto) (dto.MasterVatCodePageRespDto[dto.MasterVatCodeDto], error) {
	resp := dto.MasterVatCodePageRespDto[dto.MasterVatCodeDto]{}
	result, err := s.Repo.FindMasterVatCodeWithFilter(req)
	if err != nil {
		return resp, err
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterVatCodeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterVatCodeDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterVatCodeDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterVatCodeLatestSyncDate()
		if err != nil {
			return resp, err
		}
	}

	resp = dto.MasterVatCodePageRespDto[dto.MasterVatCodeDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterVatCodeService) UpdateMasterVatCodeStatus(req dto.MasterVatCodeUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterVatCodeFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *masterVatCodeService) SyncMasterVatCodeFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getVatCodeFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getVatCodeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncVatCode(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}
	return nil
}

func (s *masterVatCodeService) getVatCodeFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterVatCodeDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterVatCodeSyncErpRespDto](
		erpConfig.VatCodeUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterVatCodeDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.VatCode)] = dto.MasterVatCodeDto{
			CompanyCode: e.CompanyCode,
			VatCode:     e.VatCode,
			Description: e.Description,
			Vat:         *e.Vat,
			IsActive:    *e.Status == "Active",
		}
		allKeys[util.Val(e.VatCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterVatCodeService) getVatCodeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterVatCode, error) {
	dbList, err := s.Repo.FindMasterVatCodeAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterVatCode)
	for _, e := range dbList {
		dbMap[util.Val(e.VatCode)] = e
		allKeys[util.Val(e.VatCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterVatCodeService) syncVatCode(actionBy *int, erpMap map[string]dto.MasterVatCodeDto, dbMap map[string]entity.MasterVatCode, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterVatCodeRepository(tx)
		var toInsert []entity.MasterVatCode
		currentDateTime := util.Now()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.VatCode = erp.VatCode
				temp.Description = erp.Description
				temp.Vat = &erp.Vat
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterVatCodeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterVatCode{
					BaseEntity: &model.BaseEntity{
						CreatedDate: currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: &currentDateTime,
						UpdatedBy:   actionBy,
					},
					VatCode:        erp.VatCode,
					Description:    erp.Description,
					Vat:            &erp.Vat,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: &currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterVatCodeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterVatCodeList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
