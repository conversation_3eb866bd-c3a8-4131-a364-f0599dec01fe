package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_vendor_group"
)

type masterVendorGroupService struct {
	Repo repository.MasterVendorGroupRepository
}

type MasterVendorGroupService interface {
	SearchMasterVendorGroupFilter(req dto.MasterVendorGroupPageReqDto) (dto.MasterVendorGroupPageRespDto[dto.MasterVendorGroupDto], error)
	UpdateMasterVendorGroupStatus(req dto.MasterVendorGroupUpdateReqDto) error
	SyncMasterVendorGroupFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterVendorGroupService(repo repository.MasterVendorGroupRepository) MasterVendorGroupService {
	return &masterVendorGroupService{Repo: repo}
}
