package service

import (
	"content-service/internal/model/dto"

	buyerRepo "content-service/internal/repository/buyer"
	repository "content-service/internal/repository/buyer_registration_request"
)

type buyerRegistrationRequest struct {
	Repo      repository.BuyerRegistrationRequestRepository
	BuyerRepo buyerRepo.BuyerRepository
}

type BuyerRegistrationRequestService interface {
	SearchBuyerRegistrationRequestFilter(req dto.BuyerRegistrationRequestPageReqDto) (dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto], error)
	UpdateBuyerRegistrationRequestStatus(req dto.BuyerRegistrationRequestUpdateReqDto) error
}

func NewBuyerRegistrationRequestService(repo repository.BuyerRegistrationRequestRepository, buyerRepo buyerRepo.BuyerRepository) BuyerRegistrationRequestService {
	return &buyerRegistrationRequest{Repo: repo, BuyerRepo: buyerRepo}
}
