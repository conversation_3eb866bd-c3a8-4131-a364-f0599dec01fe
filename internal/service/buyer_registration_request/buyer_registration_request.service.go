package service

import (
	"content-service/internal/model/dto"

	repository "content-service/internal/repository/buyer_registration_request"
)

type buyerRegistrationRequest struct {
	Repo repository.BuyerRegistrationRequestRepository
}

type BuyerRegistrationRequestService interface {
	SearchBuyerRegistrationRequestFilter(req dto.BuyerRegistrationRequestPageReqDto) (dto.BuyerRegistrationRequestPageRespDto[dto.BuyerRegistrationRequestDto], error)
	UpdateBuyerRegistrationRequestStatus(req dto.BuyerRegistrationRequestUpdateReqDto) error
}

func NewBuyerRegistrationRequestService(repo repository.BuyerRegistrationRequestRepository) BuyerRegistrationRequestService {
	return &buyerRegistrationRequest{Repo: repo}
}
