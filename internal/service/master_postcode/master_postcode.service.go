package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_postcode"
)

type masterPostcodeService struct {
	Repo repository.MasterPostcodeRepository
}

type MasterPostcodeService interface {
	SearchMasterPostcodeFilter(req dto.MasterPostcodePageReqDto) (dto.MasterPostcodePageRespDto[dto.MasterPostcodeDto], error)
	UpdateMasterPostcodeStatus(req dto.MasterPostcodeUpdateReqDto) error
	SyncMasterPostcodeFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterPostcodeService(repo repository.MasterPostcodeRepository) MasterPostcodeService {
	return &masterPostcodeService{Repo: repo}
}
