package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_customer"
)

type masterCustomerService struct {
	Repo repository.MasterCustomerRepository
}

type MasterCustomerService interface {
	SearchMasterCustomerFilter(req dto.MasterCustomerPageReqDto) (dto.MasterCustomerPageRespDto[dto.MasterCustomerDto], error)
	UpdateMasterCustomerStatus(req dto.MasterCustomerUpdateReqDto) error
	SyncMasterCustomerFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCustomerService(repo repository.MasterCustomerRepository) MasterCustomerService {
	return &masterCustomerService{Repo: repo}
}
