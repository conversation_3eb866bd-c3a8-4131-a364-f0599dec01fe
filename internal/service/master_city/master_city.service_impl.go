package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_city"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterCityService) SearchMasterCityFilter(req dto.MasterCityPageReqDto) (dto.MasterCityPageRespDto[dto.MasterCityDto], error) {
	resp := dto.MasterCityPageRespDto[dto.MasterCityDto]{}
	result, err := s.Repo.FindMasterCityWithFilter(req)
	if err != nil {
		return resp, err
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterCityWithFilter(req)
	if err != nil {
		return resp, err
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterCityDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterCityDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterCityLatestSyncDate()
		if err != nil {
			return resp, err
		}
	}

	//NOTE - Response
	resp = dto.MasterCityPageRespDto[dto.MasterCityDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterCityService) UpdateMasterCityStatus(req dto.MasterCityUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterCityFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *masterCityService) SyncMasterCityFromErp(actionBy *int, erpConfig global.ErpConfig) error {

	erpMap, allKeys, err := s.getCustomerTypeFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getCustomerTypeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncCustomerType(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterCityService) getCustomerTypeFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterCityDto, map[string]struct{}, error) {

	erpList, err := erp.FetchListFromErp[dto.MasterCitySyncErpRespDto](
		erpConfig.CityUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterCityDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.CityCode)] = dto.MasterCityDto{
			CityCode:      e.CityCode,
			RegionCode:    e.RegionCode,
			DescriptionTh: e.DescriptionTh,
			DescriptionEn: e.DescriptionEn,
			Initial:       e.Initial,
			IsActive:      e.Status == "Active",
			CountryCode:   e.CountryCode,
		}
		allKeys[util.Val(e.CityCode)] = struct{}{}
	}

	return erpMap, allKeys, nil
}

func (s *masterCityService) getCustomerTypeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterCity, error) {
	dbList, err := s.Repo.FindMasterCityAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}
	dbMap := make(map[string]entity.MasterCity)
	for _, v := range dbList {
		dbMap[util.Val(v.CityCode)] = v
		allKeys[util.Val(v.CityCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterCityService) syncCustomerType(actionBy *int, erpMap map[string]dto.MasterCityDto, dbMap map[string]entity.MasterCity, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterCityRepository(tx)
		var toInsert []entity.MasterCity
		currentDateTime := util.Now()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.Initial = erp.Initial
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterCityAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterCity{
					BaseEntity: &model.BaseEntity{
						CreatedDate: currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: &currentDateTime,
						UpdatedBy:   actionBy,
					},
					CityCode:      erp.CityCode,
					RegionCode:    erp.RegionCode,
					DescriptionTh: erp.DescriptionTh,
					DescriptionEn: erp.DescriptionEn,
					Initial:       erp.Initial,
					IsActive:      erp.IsActive,
					CountryCode:   erp.CountryCode,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = &currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = &currentDateTime

				if err := repo.UpdateMasterCityAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterCityList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
