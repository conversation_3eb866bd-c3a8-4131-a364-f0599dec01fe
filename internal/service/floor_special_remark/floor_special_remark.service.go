package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/floor_special_remark"
)

type floorSpecialRemarkService struct {
	Repo repository.FloorSpecialRemarkRepository
}

type FloorSpecialRemarkService interface {
	GetFloorSpecialRemark(req dto.FloorSpecialRemarkSearchReqDto) (model.PagingModel[dto.FloorSpecialRemarkDto], error)
	GetFloorSpecialRemarkByID(id int) (dto.FloorSpecialRemarkDto, error)
	CreateFloorSpecialRemark(req dto.FloorSpecialRemarkReqDto) error
	UpdateFloorSpecialRemark(req dto.FloorSpecialRemarkReqDto) error
	UpdateFloorSpecialRemarkStatus(req dto.FloorSpecialRemarkReqDto) error
	DeleteFloorSpecialRemark(id int) error
}

func NewFloorSpecialRemarkService(repo repository.FloorSpecialRemarkRepository) FloorSpecialRemarkService {
	return &floorSpecialRemarkService{Repo: repo}
}
