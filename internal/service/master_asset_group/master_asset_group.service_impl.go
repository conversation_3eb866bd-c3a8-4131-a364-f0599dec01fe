package service

import (
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_group"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterAssetGroupService) SearchMasterAssetGroupFilter(req dto.MasterAssetGroupPageReqDto) (dto.MasterAssetGroupPageRespDto[dto.MasterAssetGroupDto], error) {
	resp := dto.MasterAssetGroupPageRespDto[dto.MasterAssetGroupDto]{}
	result, err := s.Repo.FindMasterAssetGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterAssetGroupWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterAssetGroupDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterAssetGroupDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterAssetGroupLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterAssetGroupPageRespDto[dto.MasterAssetGroupDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterAssetGroupService) UpdateMasterAssetGroupStatus(req dto.MasterAssetGroupUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterAssetGroupFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterAssetGroupService) SyncMasterAssetGroupFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getAssetGroupFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getAssetGroupFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncAssetGroup(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterAssetGroupService) getAssetGroupFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterAssetGroupDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterAssetGroupSyncErpRespDto](
		erpConfig.AssetGroupUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterAssetGroupDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.AssetGroupCode)] = dto.MasterAssetGroupDto{
			CompanyCode:    e.CompanyCode,
			AssetTypeCode:  e.AssetTypeCode,
			AssetGroupCode: e.AssetGroupCode,
			DescriptionTh:  e.DescriptionTh,
			DescriptionEn:  e.DescriptionEn,
			IsActive:       e.Status == "Active",
		}
		allKeys[util.Val(e.AssetGroupCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterAssetGroupService) getAssetGroupFromDb(allKeys map[string]struct{}) (map[string]entity.MasterAssetGroup, error) {
	dbList, err := s.Repo.FindMasterAssetGroupAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterAssetGroup)
	for _, e := range dbList {
		dbMap[util.Val(e.AssetGroupCode)] = e
		allKeys[util.Val(e.AssetGroupCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterAssetGroupService) syncAssetGroup(actionBy *int, erpMap map[string]dto.MasterAssetGroupDto, dbMap map[string]entity.MasterAssetGroup, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterAssetGroupRepository(tx)
		var toInsert []entity.MasterAssetGroup
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.AssetTypeCode = erp.AssetTypeCode
				temp.AssetGroupCode = erp.AssetGroupCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterAssetGroup{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					AssetTypeCode:  erp.AssetTypeCode,
					AssetGroupCode: erp.AssetGroupCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetGroupAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterAssetGroupList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}

func (s *masterAssetGroupService) FindMasterAssetGroupAll() ([]entity.MasterAssetGroup, error) {
	result, err := s.Repo.FindMasterAssetGroupAll()
	if err != nil {
		return result, errs.NewError(http.StatusInternalServerError, err)
	}

	return result, nil
}
