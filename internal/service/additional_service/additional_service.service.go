package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/additional_service"
	serviceAssetRepo "content-service/internal/repository/additional_service_asset"
	serviceCustomerGroupRepo "content-service/internal/repository/additional_service_customer_group"
	serviceDisplayRepo "content-service/internal/repository/additional_service_display"
	serviceInterestRepo "content-service/internal/repository/additional_service_interest"
	serviceProviderRepo "content-service/internal/repository/additional_service_provider"
)

type additionalServiceService struct {
	Repo                 repository.AdditionalServiceRepository
	ServiceAssetRepo     serviceAssetRepo.AdditionalServiceAssetRepository
	ServiceCustomerGroup serviceCustomerGroupRepo.AdditionalServiceCustomerGroupRepository
	ServiceProviderRepo  serviceProviderRepo.AdditionalServiceProviderRepository
	ServiceDisplayRepo   serviceDisplayRepo.AdditionalServiceDisplayRepository
	ServiceInterestRepo  serviceInterestRepo.AdditionalServiceInterestRepository
}

type AdditionalServiceService interface {
	GetAdditionalService(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceDto], error)
	GetAdditionalServiceByID(id int) (dto.AdditionalServiceDto, error)
	CreateAdditionalService(req dto.AdditionalServiceDto, actionBy *int) error
	UpdateAdditionalService(req dto.AdditionalServiceDto, actionBy *int) error
	UpdateAdditionalServiceStatus(req dto.AdditionalServiceDto, actionBy *int) error
	DeleteAdditionalService(id int, actionBy *int) error

	GetAdditionalServiceInterest(req model.PagingRequest) (model.PagingModel[dto.AdditionalServiceInterestDto], error)
}

func NewAdditionalServiceService(
	repo repository.AdditionalServiceRepository,
	serviceAssetRepo serviceAssetRepo.AdditionalServiceAssetRepository,
	serviceCustomerGroupRepo serviceCustomerGroupRepo.AdditionalServiceCustomerGroupRepository,
	serviceProviderRepo serviceProviderRepo.AdditionalServiceProviderRepository,
	serviceDisplayRepo serviceDisplayRepo.AdditionalServiceDisplayRepository,
	serviceInterestRepo serviceInterestRepo.AdditionalServiceInterestRepository,
) AdditionalServiceService {
	return &additionalServiceService{
		Repo:                 repo,
		ServiceAssetRepo:     serviceAssetRepo,
		ServiceCustomerGroup: serviceCustomerGroupRepo,
		ServiceProviderRepo:  serviceProviderRepo,
		ServiceDisplayRepo:   serviceDisplayRepo,
		ServiceInterestRepo:  serviceInterestRepo,
	}
}
