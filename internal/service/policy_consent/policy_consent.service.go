package service

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/policy_consent"
)

type policyConsentService struct {
	Repo repository.PolicyConsentRepository
}

type PolicyConsentService interface {
	GetPolicyConsent(req model.PagingRequest, consentType string) (dto.PolicyPageResDto, error)
	GetPolicyConsentByID(id int) (dto.PolicyConsentDto, error)
	CreatePolicyConsent(req dto.PolicyConsentReqDto, reqConsentType string) error
	UpdatePolicyConsent(req dto.PolicyConsentReqDto, reqConsentType string) error
	UpdatePolicyConsentStatus(req dto.PolicyConsentReqDto) error
	DeletePolicyConsent(id int) error
	ValidateOverlapStartDate(req dto.PolicyConsentReqDto, reqConsentType string) error
}

func NewPolicyConsentService(repo repository.PolicyConsentRepository) PolicyConsentService {
	return &policyConsentService{Repo: repo}
}
