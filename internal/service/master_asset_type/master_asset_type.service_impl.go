package service

import (
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_asset_type"
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterAssetTypeService) SearchMasterAssetTypeFilter(req dto.MasterAssetTypePageReqDto) (dto.MasterAssetTypePageRespDto[dto.MasterAssetTypeDto], error) {
	resp := dto.MasterAssetTypePageRespDto[dto.MasterAssetTypeDto]{}
	result, err := s.Repo.FindMasterAssetTypeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterAssetTypeWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterAssetTypeDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterAssetTypeDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterAssetTypeLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterAssetTypePageRespDto[dto.MasterAssetTypeDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterAssetTypeService) UpdateMasterAssetTypeStatus(req dto.MasterAssetTypeUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterAssetTypeFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterAssetTypeService) SyncMasterAssetTypeFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getAssetTypeFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getAssetTypeFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncAssetType(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterAssetTypeService) getAssetTypeFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterAssetTypeDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterAssetTypeSyncErpRespDto](
		erpConfig.AssetTypeUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterAssetTypeDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.AssetTypeCode)] = dto.MasterAssetTypeDto{
			CompanyCode:            e.CompanyCode,
			AssetTypeCode:          e.AssetTypeCode,
			DescriptionTh:          e.DescriptionTh,
			DescriptionEn:          e.DescriptionEn,
			IsActive:               e.Status == "Active",
			AssessmentTemplateCode: e.AssessmentTemplateCode,
			NoOfDayAssessment:      e.NoOfDayAssessment,
			MinimumDepositType:     e.MinimumDepositType,
			MinimumDepositRate:     e.MinimumDepositRate,
			MinimumDepositAmount:   e.MinimumDepositAmount,
			ReceiveAssetReport:     e.ReceiveAssetReport,
		}
		allKeys[util.Val(e.AssetTypeCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterAssetTypeService) getAssetTypeFromDb(allKeys map[string]struct{}) (map[string]entity.MasterAssetType, error) {
	dbList, err := s.Repo.FindMasterAssetTypeAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterAssetType)
	for _, e := range dbList {
		dbMap[util.Val(e.AssetTypeCode)] = e
		allKeys[util.Val(e.AssetTypeCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterAssetTypeService) syncAssetType(actionBy *int, erpMap map[string]dto.MasterAssetTypeDto, dbMap map[string]entity.MasterAssetType, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterAssetTypeRepository(tx)
		var toInsert []entity.MasterAssetType
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.AssetTypeCode = erp.AssetTypeCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.CompanyCode = erp.CompanyCode
				temp.AssessmentTemplateCode = erp.AssessmentTemplateCode
				temp.NoOfDayAssessment = erp.NoOfDayAssessment
				temp.MinimumDepositType = erp.MinimumDepositType
				temp.MinimumDepositRate = erp.MinimumDepositRate
				temp.MinimumDepositAmount = erp.MinimumDepositAmount
				temp.ReceiveAssetReport = erp.ReceiveAssetReport
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterAssetType{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					AssetTypeCode:          erp.AssetTypeCode,
					DescriptionTh:          erp.DescriptionTh,
					DescriptionEn:          erp.DescriptionEn,
					IsActive:               erp.IsActive,
					CompanyCode:            erp.CompanyCode,
					AssessmentTemplateCode: erp.AssessmentTemplateCode,
					NoOfDayAssessment:      erp.NoOfDayAssessment,
					MinimumDepositType:     erp.MinimumDepositType,
					MinimumDepositRate:     erp.MinimumDepositRate,
					MinimumDepositAmount:   erp.MinimumDepositAmount,
					ReceiveAssetReport:     erp.ReceiveAssetReport,
					IsDeletedByErp:         false,
					LatestSyncDate:         currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterAssetTypeAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterAssetTypeList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}

func (s *masterAssetTypeService) FindMasterAssetTypeAll() ([]entity.MasterAssetType, error) {
	result, err := s.Repo.FindMasterAssetTypeAll()
	if err != nil {
		return result, errs.NewError(http.StatusInternalServerError, err)
	}

	return result, nil
}
