package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_branch"
)

type masterBranchService struct {
	Repo repository.MasterBranchRepository
}

type MasterBranchService interface {
	GetMasterBranchAll() (dto.MasterBranchListDto, error)
	SearchMasterBranchFilter(req dto.MasterBranchPageReqDto) (dto.MasterBranchPageRespDto[dto.MasterBranchDto], error)
	UpdateMasterBranchStatus(req dto.MasterBranchUpdateReqDto) error
	SyncMasterBranchFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterBranchService(repo repository.MasterBranchRepository) MasterBranchService {
	return &masterBranchService{Repo: repo}
}
