package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_customer_type"
)

type masterCustomerTypeService struct {
	Repo repository.MasterCustomerTypeRepository
}

type MasterCustomerTypeService interface {
	SearchMasterCustomerTypeFilter(req dto.MasterCustomerTypePageReqDto) (dto.MasterCustomerTypePageRespDto[dto.MasterCustomerTypeDto], error)
	UpdateMasterCustomerTypeStatus(req dto.MasterCustomerTypeUpdateReqDto) error
	SyncMasterCustomerTypeFromErp(ActionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCustomerTypeService(repo repository.MasterCustomerTypeRepository) MasterCustomerTypeService {
	return &masterCustomerTypeService{Repo: repo}
}
