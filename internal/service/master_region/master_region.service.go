package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_region"
)

type masterRegionService struct {
	Repo repository.MasterRegionRepository
}

type MasterRegionService interface {
	SearchMasterRegionFilter(req dto.MasterRegionPageReqDto) (dto.MasterRegionPageRespDto[dto.MasterRegionDto], error)
	UpdateMasterRegionStatus(req dto.MasterRegionUpdateReqDto) error
	SyncMasterRegionFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterRegionService(repo repository.MasterRegionRepository) MasterRegionService {
	return &masterRegionService{Repo: repo}
}
