package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	"errors"
	"fmt"
	"net/http"

	"gorm.io/gorm"
)

func (s *proxyBidCancelReasonService) GetProxyBidCancelReason(req model.PagingRequest) (model.PagingModel[dto.ProxyBidCancelReasonDto], error) {
	resp := model.PagingModel[dto.ProxyBidCancelReasonDto]{}

	//NOTE - Get List
	result, err := s.Repo.FindAllProxyBidCancelReason(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountAllProxyBidCancelReason()
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.ProxyBidCancelReasonDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.ProxyBidCancelReasonDto](v)
	}

	//NOTE - Response
	resp = *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit)

	return resp, nil
}

func (s *proxyBidCancelReasonService) GetProxyBidCancelReasonByID(id int) (dto.ProxyBidCancelReasonDto, error) {
	resp := dto.ProxyBidCancelReasonDto{}
	result, err := s.Repo.FindProxyBidCancelReasonByID(id)
	if err != nil {
		if errs.IsGormNotFound(err) {
			return dto.ProxyBidCancelReasonDto{}, errs.NewError(http.StatusNotFound, err)
		}
		return dto.ProxyBidCancelReasonDto{}, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.ProxyBidCancelReasonDto](result)
	return resp, nil
}

func validateProxyBidCancelReason(proxyBidCancelReason dto.ProxyBidCancelReasonReqDto) error {
	if proxyBidCancelReason.Reason == nil || util.Val(proxyBidCancelReason.Reason) == "" {
		return errs.NewError(http.StatusBadRequest, fmt.Errorf("reason is required"))
	}
	return nil
}

func (s *proxyBidCancelReasonService) CreateProxyBidCancelReason(req dto.ProxyBidCancelReasonReqDto) error {
	now := util.Now()

	//NOTE - Check Required fields
	err := validateProxyBidCancelReason(req)
	if err != nil {
		return err
	}

	//NOTE - Check for duplicate customer_group_id
	exist, err := s.Repo.FindByReason(util.Val(req.Reason), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "reason is duplicated", "error.proxyBidCancelReason.duplicateReason")
	} else if err != nil && !errs.IsGormNotFound(err) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Create Payment Due Notification
	entityProxyBidCancelReason := util.MapToPtr[entity.ProxyBidCancelReason](req)
	if entityProxyBidCancelReason == nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.BadRequest, "Invalid request data", "")
	}

	entityProxyBidCancelReason.BaseEntity = &model.BaseEntity{
		CreatedBy:   req.ActionBy,
		CreatedDate: now,
		UpdatedBy:   req.ActionBy,
		UpdatedDate: &now,
	}

	if err := s.Repo.InsertProxyBidCancelReason(entityProxyBidCancelReason); err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	return nil
}

func (s *proxyBidCancelReasonService) UpdateProxyBidCancelReason(req dto.ProxyBidCancelReasonReqDto) error {
	//NOTE - Check Required fields
	if err := validateProxyBidCancelReason(req); err != nil {
		return errs.NewError(http.StatusBadRequest, err)
	}

	//NOTE - check duplicate
	exist, err := s.Repo.FindByReason(util.Val(req.Reason), req.Id)
	if err == nil && exist != nil {
		return errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "reason is duplicated", "error.proxyBidCancelReason.duplicateReason")
	} else if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return errs.NewError(http.StatusInternalServerError, err)
	}

	now := util.Now()
	fieldsToUpdate := map[string]interface{}{
		"reason":       req.Reason,
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": &now,
	}

	affectedRows, err := s.Repo.UpdateProxyBidCancelReason(req.Id, fieldsToUpdate)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}
	return nil
}

func (s *proxyBidCancelReasonService) UpdateProxyBidCancelReasonStatus(req dto.ProxyBidCancelReasonReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdateProxyBidCancelReason(req.Id, fieldsToUpdate)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *proxyBidCancelReasonService) DeleteProxyBidCancelReason(id int) error {
	affectedRows, err := s.Repo.DeleteProxyBidCancelReason(id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(http.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", id), "")
	}
	return nil
}
