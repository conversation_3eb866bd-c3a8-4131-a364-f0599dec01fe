package service

import (
	"fmt"
	"net/http"
	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_register_type_car"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterRegisterTypeCarService) SearchMasterRegisterTypeCarFilter(req dto.MasterRegisterTypeCarPageReqDto) (dto.MasterRegisterTypeCarPageRespDto[dto.MasterRegisterTypeCarDto], error) {
	resp := dto.MasterRegisterTypeCarPageRespDto[dto.MasterRegisterTypeCarDto]{}
	result, err := s.Repo.FindMasterRegisterTypeCarWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterRegisterTypeCarWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterRegisterTypeCarDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterRegisterTypeCarDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterRegisterTypeCarLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterRegisterTypeCarPageRespDto[dto.MasterRegisterTypeCarDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterRegisterTypeCarService) UpdateMasterRegisterTypeCarStatus(req dto.MasterRegisterTypeCarUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterRegisterTypeCarFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterRegisterTypeCarService) SyncMasterRegisterTypeCarFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getRegisterTypeCarFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getRegisterTypeCarFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncRegisterTypeCar(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterRegisterTypeCarService) getRegisterTypeCarFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterRegisterTypeCarDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterRegisterTypeCarSyncErpRespDto](
		erpConfig.RegisterTypeCarUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)

	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterRegisterTypeCarDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {

		erpMap[util.Val(e.AssetRegisterTypeCode)] = dto.MasterRegisterTypeCarDto{
			CompanyCode:           e.CompanyCode,
			IsActive:              e.Status == "Active",
			AssetRegisterTypeCode: e.AssetRegisterTypeCode,
			DescriptionTh:         e.DescriptionTh,
			DescriptionEn:         e.DescriptionEn,
			IsDeletedByErp:        false,
		}
		allKeys[util.Val(e.AssetRegisterTypeCode)] = struct{}{}
	}

	return erpMap, allKeys, nil
}

func (s *masterRegisterTypeCarService) getRegisterTypeCarFromDb(allKeys map[string]struct{}) (map[string]entity.MasterRegisterTypeCar, error) {
	dbList, err := s.Repo.FindMasterRegisterTypeCarAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterRegisterTypeCar)
	for _, e := range dbList {
		dbMap[util.Val(e.AssetRegisterTypeCode)] = e
		allKeys[util.Val(e.AssetRegisterTypeCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterRegisterTypeCarService) syncRegisterTypeCar(actionBy *int, erpMap map[string]dto.MasterRegisterTypeCarDto, dbMap map[string]entity.MasterRegisterTypeCar, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterRegisterTypeCarRepository(tx)
		var toInsert []entity.MasterRegisterTypeCar
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.AssetRegisterTypeCode = erp.AssetRegisterTypeCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegisterTypeCarAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterRegisterTypeCar{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:           erp.CompanyCode,
					AssetRegisterTypeCode: erp.AssetRegisterTypeCode,
					DescriptionTh:         erp.DescriptionTh,
					DescriptionEn:         erp.DescriptionEn,
					IsActive:              erp.IsActive,
					IsDeletedByErp:        false,
					LatestSyncDate:        currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterRegisterTypeCarAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterRegisterTypeCarList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
