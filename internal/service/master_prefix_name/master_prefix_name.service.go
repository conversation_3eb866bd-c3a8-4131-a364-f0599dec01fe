package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_prefix_name"
)

type masterPrefixNameService struct {
	Repo repository.MasterPrefixNameRepository
}

type MasterPrefixNameService interface {
	SearchMasterPrefixNameFilter(req dto.MasterPrefixNamePageReqDto) (dto.MasterPrefixNamePageRespDto[dto.MasterPrefixNameDto], error)
	UpdateMasterPrefixNameStatus(req dto.MasterPrefixNameUpdateReqDto) error
	SyncMasterPrefixNameFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterPrefixNameService(repo repository.MasterPrefixNameRepository) MasterPrefixNameService {
	return &masterPrefixNameService{Repo: repo}
}
