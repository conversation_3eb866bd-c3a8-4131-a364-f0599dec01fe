package service

import (
	"content-service/internal/model/dto"
	"errors"
	"fmt"
	"net/http"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/util"

	"gorm.io/gorm"
)

func (s *userBidderNumbersService) GetUserBidderNumberByBidderNumber(bidderNumberId string) (dto.UserBidderNumbersRespDto, error) {
	resp := dto.UserBidderNumbersRespDto{}
	result, err := s.Repo.FindUserBidderNumberByBidderNumber(bidderNumberId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return resp, errs.NewBusinessError(http.StatusBadRequest, constant.Invalidate, "bidder number not found", "error.userBidderNumbers.notFound")
		}
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	resp = util.MapToWithCreatedByAndUpdatedBy[dto.UserBidderNumbersRespDto](result)
	resp.UserId = result.UserId
	resp.BidderNumber = result.BidderNumber
	resp.BuyerName = util.Ptr(fmt.Sprintf("%s %s %s %s", util.Val(result.PrefixName), util.Val(result.BuyerFirstName), util.Val(result.BuyerMiddleName), util.Val(result.BuyerLastName)))

	return resp, nil
}
