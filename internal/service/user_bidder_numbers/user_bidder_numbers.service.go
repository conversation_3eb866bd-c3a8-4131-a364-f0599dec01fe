package service

import (
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/user_bidder_numbers"
)

type userBidderNumbersService struct {
	Repo repository.UserBidderNumbersRepository
}

type UserBidderNumbersService interface {
	GetUserBidderNumberByBidderNumber(bidderNumberId string) (dto.UserBidderNumbersRespDto, error)
}

func NewUserBidderNumbersService(repo repository.UserBidderNumbersRepository) UserBidderNumbersService {
	return &userBidderNumbersService{Repo: repo}
}
