package service

import (
	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_holiday"
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterHolidayService) SearchMasterHolidayFilter(req dto.MasterHolidayPageReqDto) (dto.MasterHolidayPageRespDto[dto.MasterHolidayDto], error) {
	resp := dto.MasterHolidayPageRespDto[dto.MasterHolidayDto]{}
	result, err := s.Repo.FindMasterHolidayWithFilter(req)
	if err != nil {
		return resp, errs.NewError(fiber.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterHolidayWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterHolidayDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterHolidayDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(fiber.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterHolidayPageRespDto[dto.MasterHolidayDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterHolidayService) UpdateMasterHolidayStatus(req dto.MasterHolidayUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterHolidayFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterHolidayService) SyncMasterHolidayFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getHolidayFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getHolidayFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncHoliday(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterHolidayService) getHolidayFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterHolidayDto, map[string]struct{}, error) {
	layout := "02/01/2006"
	erpList, err := erp.FetchListFromErp[dto.MasterHolidaySyncErpRespDto](
		erpConfig.HolidayUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterHolidayDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		dateStr := e.Date.Format(layout)
		erpMap[dateStr] = dto.MasterHolidayDto{
			CompanyCode:   e.CompanyCode,
			Date:          e.Date.ToTime(),
			Day:           e.Day,
			DescriptionTh: e.DescriptionTh,
			IsActive:      e.Status == "Active",
		}
		allKeys[dateStr] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterHolidayService) getHolidayFromDb(allKeys map[string]struct{}) (map[string]entity.MasterHoliday, error) {
	layout := "02/01/2006"
	dbList, err := s.Repo.FindMasterHolidayAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterHoliday)
	for _, e := range dbList {
		dateStr := e.Date.Format(layout)
		dbMap[dateStr] = e
		allKeys[dateStr] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterHolidayService) syncHoliday(actionBy *int, erpMap map[string]dto.MasterHolidayDto, dbMap map[string]entity.MasterHoliday, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterHolidayRepository(tx)
		var toInsert []entity.MasterHoliday
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.CompanyCode = erp.CompanyCode
				temp.Date = erp.Date
				temp.Day = erp.Day
				temp.DescriptionTh = erp.DescriptionTh
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterHolidayAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterHoliday{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					CompanyCode:    erp.CompanyCode,
					Date:           erp.Date,
					Day:            erp.Day,
					DescriptionTh:  erp.DescriptionTh,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterHolidayAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterHolidayList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
