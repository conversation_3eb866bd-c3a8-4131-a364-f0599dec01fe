package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_sale_channel"
)

type masterSaleChannelService struct {
	Repo repository.MasterSaleChannelRepository
}

type MasterSaleChannelService interface {
	SearchMasterSaleChannelFilter(req dto.MasterSaleChannelPageReqDto) (dto.MasterSaleChannelPageRespDto[dto.MasterSaleChannelDto], error)
	UpdateMasterSaleChannelStatus(req dto.MasterSaleChannelUpdateReqDto) error
	SyncMasterSaleChannelFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterSaleChannelService(repo repository.MasterSaleChannelRepository) MasterSaleChannelService {
	return &masterSaleChannelService{Repo: repo}
}
