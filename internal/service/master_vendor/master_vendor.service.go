package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_vendor"
)

type masterVendorService struct {
	Repo repository.MasterVendorRepository
}

type MasterVendorService interface {
	SearchMasterVendorFilter(req dto.MasterVendorPageReqDto) (dto.MasterVendorPageRespDto[dto.MasterVendorDto], error)
	UpdateMasterVendorStatus(req dto.MasterVendorUpdateReqDto) error
	SyncMasterVendorFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterVendorService(repo repository.MasterVendorRepository) MasterVendorService {
	return &masterVendorService{Repo: repo}
}
