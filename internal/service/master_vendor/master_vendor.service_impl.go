package service

import (
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_vendor"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterVendorService) SearchMasterVendorFilter(req dto.MasterVendorPageReqDto) (dto.MasterVendorPageRespDto[dto.MasterVendorDto], error) {
	resp := dto.MasterVendorPageRespDto[dto.MasterVendorDto]{}
	result, err := s.Repo.FindMasterVendorWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterVendorWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterVendorDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterVendorDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterVendorLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterVendorPageRespDto[dto.MasterVendorDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterVendorService) UpdateMasterVendorStatus(req dto.MasterVendorUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterVendorFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterVendorService) SyncMasterVendorFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getVendorFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getVendorFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncVendor(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterVendorService) getVendorFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterVendorDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterVendorSyncErpRespDto](
		erpConfig.VendorUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterVendorDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.VendorNo)] = dto.MasterVendorDto{
			VendorNo:          e.VendorNo,
			VendorName:        e.VendorName,
			VendorGroup:       e.VendorGroup,
			VatRegistrationNo: e.VatRegistrationNo,
			PrefixNameCode:    e.PrefixNameCode,
			IsActive:          e.Status == "Active",
		}
		allKeys[util.Val(e.VendorNo)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterVendorService) getVendorFromDb(allKeys map[string]struct{}) (map[string]entity.MasterVendor, error) {
	dbList, err := s.Repo.FindMasterVendorAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterVendor)
	for _, e := range dbList {
		dbMap[util.Val(e.VendorNo)] = e
		allKeys[util.Val(e.VendorNo)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterVendorService) syncVendor(actionBy *int, erpMap map[string]dto.MasterVendorDto, dbMap map[string]entity.MasterVendor, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterVendorRepository(tx)
		var toInsert []entity.MasterVendor
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.VendorNo = erp.VendorNo
				temp.VendorName = erp.VendorName
				temp.VendorGroup = erp.VendorGroup
				temp.VatRegistrationNo = erp.VatRegistrationNo
				temp.PrefixNameCode = erp.PrefixNameCode
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVendorAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterVendor{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					VendorNo:          erp.VendorNo,
					VendorName:        erp.VendorName,
					VendorGroup:       erp.VendorGroup,
					VatRegistrationNo: erp.VatRegistrationNo,
					PrefixNameCode:    erp.PrefixNameCode,
					IsActive:          erp.IsActive,
					IsDeletedByErp:    false,
					LatestSyncDate:    currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterVendorAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterVendorList(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}
		return nil
	})
}
