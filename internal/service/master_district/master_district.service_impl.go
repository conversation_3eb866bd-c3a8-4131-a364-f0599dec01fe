package service

import (
	"content-service/internal/global"
	"content-service/internal/integration/erp"
	"content-service/internal/model/dto"
	"content-service/internal/model/entity"
	repository "content-service/internal/repository/master_district"
	"fmt"
	"net/http"

	"time"

	"backend-common-lib/constant"
	"backend-common-lib/errs"
	"backend-common-lib/model"
	"backend-common-lib/util"

	"github.com/gofiber/fiber/v2"
	"gorm.io/gorm"
)

func (s *masterDistrictService) SearchMasterDistrictFilter(req dto.MasterDistrictPageReqDto) (dto.MasterDistrictPageRespDto[dto.MasterDistrictDto], error) {
	resp := dto.MasterDistrictPageRespDto[dto.MasterDistrictDto]{}
	result, err := s.Repo.FindMasterDistrictWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Count
	count, err := s.Repo.CountMasterDistrictWithFilter(req)
	if err != nil {
		return resp, errs.NewError(http.StatusInternalServerError, err)
	}

	//NOTE - Map to DTO
	mapResult := make([]dto.MasterDistrictDto, len(result))
	for i, v := range result {
		mapResult[i] = util.MapToWithCreatedByAndUpdatedBy[dto.MasterDistrictDto](v)
	}

	//NOTE Get latest sync date
	var latestSyncDate *time.Time
	if len(result) > 0 {
		latestSyncDate = result[0].LatestSyncDate
	} else {
		latestSyncDate, err = s.Repo.FindMasterDistrictLatestSyncDate()
		if err != nil {
			return resp, errs.NewError(http.StatusInternalServerError, err)
		}
	}

	//NOTE - Response
	resp = dto.MasterDistrictPageRespDto[dto.MasterDistrictDto]{
		PagingModel:    *util.MapPaginationResult(mapResult, int(count), (req.PageNumber-1)*req.PageLimit, req.PageLimit),
		LatestSyncDate: latestSyncDate,
	}

	return resp, nil
}

func (s *masterDistrictService) UpdateMasterDistrictStatus(req dto.MasterDistrictUpdateReqDto) error {
	fieldsToUpdate := map[string]interface{}{
		"is_active":    req.IsActive,
		"updated_by":   req.ActionBy,
		"updated_date": util.Now(),
	}

	affectedRows, err := s.Repo.UpdatesMasterDistrictFieldsWhere(fieldsToUpdate, "id = ?", req.Id)
	if err != nil {
		return errs.NewError(http.StatusInternalServerError, err)
	}
	if affectedRows == 0 {
		return errs.NewBusinessError(fiber.StatusNotFound, constant.NotFound, fmt.Sprintf("id %d not found", req.Id), "")
	}

	return nil
}

func (s *masterDistrictService) SyncMasterDistrictFromErp(actionBy *int, erpConfig global.ErpConfig) error {
	erpMap, allKeys, err := s.getDistrictFromErp(erpConfig)
	if err != nil {
		return err
	}
	dbMap, err := s.getDistrictFromDb(allKeys)
	if err != nil {
		return err
	}
	err = s.syncDistrict(actionBy, erpMap, dbMap, allKeys)
	if err != nil {
		return err
	}

	return nil
}

func (s *masterDistrictService) getDistrictFromErp(erpConfig global.ErpConfig) (map[string]dto.MasterDistrictDto, map[string]struct{}, error) {
	erpList, err := erp.FetchListFromErp[dto.MasterDistrictSyncErpRespDto](
		erpConfig.DistrictUrl,
		erpConfig.Token,
		map[string]interface{}{"Company_Name": "AUCT"},
		"data",
	)
	if err != nil {
		return nil, nil, errs.NewError(http.StatusInternalServerError, err)
	}

	erpMap := make(map[string]dto.MasterDistrictDto)
	allKeys := make(map[string]struct{})

	for _, e := range erpList {
		erpMap[util.Val(e.DistrictCode)] = dto.MasterDistrictDto{
			RegionCode:    e.RegionCode,
			CityCode:      e.CityCode,
			DistrictCode:  e.DistrictCode,
			DescriptionTh: e.DescriptionTh,
			DescriptionEn: e.DescriptionEn,
			IsActive:      e.Status == "Active",
		}
		allKeys[util.Val(e.DistrictCode)] = struct{}{}
	}
	return erpMap, allKeys, nil
}

func (s *masterDistrictService) getDistrictFromDb(allKeys map[string]struct{}) (map[string]entity.MasterDistrict, error) {
	dbList, err := s.Repo.FindMasterDistrictAll()
	if err != nil {
		return nil, errs.NewError(http.StatusInternalServerError, err)
	}

	dbMap := make(map[string]entity.MasterDistrict)
	for _, e := range dbList {
		dbMap[util.Val(e.DistrictCode)] = e
		allKeys[util.Val(e.DistrictCode)] = struct{}{}
	}
	return dbMap, nil
}

func (s *masterDistrictService) syncDistrict(actionBy *int, erpMap map[string]dto.MasterDistrictDto, dbMap map[string]entity.MasterDistrict, allKeys map[string]struct{}) error {
	return util.WithTx(s.Repo.GetDB(), func(tx *gorm.DB) error {
		repo := repository.NewMasterDistrictRepository(tx)
		var toInsert []entity.MasterDistrict
		currentDateTime := util.NowPtr()

		for code := range allKeys {
			erp, erpExists := erpMap[code]
			db, dbExists := dbMap[code]

			if erpExists && dbExists {
				// มีทั้ง ERP + DB → compare → update หมด
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.RegionCode = erp.RegionCode
				temp.CityCode = erp.CityCode
				temp.DistrictCode = erp.DistrictCode
				temp.DescriptionTh = erp.DescriptionTh
				temp.DescriptionEn = erp.DescriptionEn
				temp.IsActive = erp.IsActive
				temp.IsDeletedByErp = false
				temp.LatestSyncDate = currentDateTime

				if changes := util.CompareStructDeep(db, temp, ""); len(changes) > 0 {
					if err := repo.UpdateMasterDistrictAllFields(&temp); err != nil {
						return errs.NewError(http.StatusInternalServerError, err)
					}
				}

			} else if erpExists && !dbExists {
				// มีใน ERP แต่ไม่มีใน DB → insert
				temp := entity.MasterDistrict{
					BaseEntity: &model.BaseEntity{
						CreatedDate: *currentDateTime,
						CreatedBy:   actionBy,
						UpdatedDate: currentDateTime,
						UpdatedBy:   actionBy,
					},
					RegionCode:     erp.RegionCode,
					CityCode:       erp.CityCode,
					DistrictCode:   erp.DistrictCode,
					DescriptionTh:  erp.DescriptionTh,
					DescriptionEn:  erp.DescriptionEn,
					IsActive:       erp.IsActive,
					IsDeletedByErp: false,
					LatestSyncDate: currentDateTime,
				}
				toInsert = append(toInsert, temp)

			} else if !erpExists && dbExists {
				// มีใน DB แต่ไม่มีใน ERP → mark delete
				temp := db
				temp.UpdatedDate = currentDateTime
				temp.UpdatedBy = actionBy
				temp.IsActive = false
				temp.IsDeletedByErp = true
				temp.LatestSyncDate = currentDateTime

				if err := repo.UpdateMasterDistrictAllFields(&temp); err != nil {
					return errs.NewError(http.StatusInternalServerError, err)
				}
			}
		}

		if len(toInsert) > 0 {
			if err := repo.InsertMasterDistrictListWithBatches(toInsert); err != nil {
				return errs.NewError(http.StatusInternalServerError, err)
			}
		}

		_, err := repo.UpdatesMasterDistrictFieldsWhere(map[string]interface{}{"latest_sync_date": currentDateTime, "updated_date": currentDateTime, "updated_by": actionBy}, "is_deleted_by_erp = ?", false)
		if err != nil {
			return errs.NewError(http.StatusInternalServerError, err)
		}

		return nil
	})
}
