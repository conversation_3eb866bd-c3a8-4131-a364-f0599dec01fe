package service_test

import (
	"errors"
	"testing"

	"content-service/internal/model/entity"
	"content-service/internal/service"

	"github.com/stretchr/testify/assert"
)

// Mock implementation of SampleRepository interface
type MockSampleRepository struct {
	FakeData []entity.Fruit
	Err      error
}

func (m *MockSampleRepository) GetAll() ([]entity.Fruit, error) {
	if m.Err != nil {
		return nil, m.Err
	}
	return m.FakeData, nil
}

func TestSampleService_GetAll_Success(t *testing.T) {
	mockRepo := &MockSampleRepository{
		FakeData: []entity.Fruit{
			{Name: "Apple", Color: "Red"},
			{Name: "Banana", Color: "Yellow"},
		},
	}

	svc := service.SampleService{Repo: mockRepo}

	result, err := svc.GetAll()

	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, "Apple", result[0].Name)
}

func TestSampleService_GetAll_Error(t *testing.T) {
	mockRepo := &MockSampleRepository{
		Err: errors.New("db error"),
	}

	svc := service.SampleService{Repo: mockRepo}

	result, err := svc.GetAll()

	assert.Error(t, err)
	assert.Nil(t, result)
}
