package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_country"
)

type masterCountryService struct {
	Repo repository.MasterCountryRepository
}

type MasterCountryService interface {
	SearchMasterCountryFilter(req dto.MasterCountryPageReqDto) (dto.MasterCountryPageRespDto[dto.MasterCountryDto], error)
	UpdateMasterCountryStatus(req dto.MasterCountryUpdateReqDto) error
	SyncMasterCountryFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterCountryService(repo repository.MasterCountryRepository) MasterCountryService {
	return &masterCountryService{Repo: repo}
}
