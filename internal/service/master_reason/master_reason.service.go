package service

import (
	"content-service/internal/global"
	"content-service/internal/model/dto"
	repository "content-service/internal/repository/master_reason"
)

type masterReasonService struct {
	Repo repository.MasterReasonRepository
}

type MasterReasonService interface {
	SearchMasterReasonFilter(req dto.MasterReasonPageReqDto) (dto.MasterReasonPageRespDto[dto.MasterReasonDto], error)
	UpdateMasterReasonStatus(req dto.MasterReasonUpdateReqDto) error
	SyncMasterReasonFromErp(actionBy *int, erpConfig global.ErpConfig) error
}

func NewMasterReasonService(repo repository.MasterReasonRepository) MasterReasonService {
	return &masterReasonService{Repo: repo}
}
