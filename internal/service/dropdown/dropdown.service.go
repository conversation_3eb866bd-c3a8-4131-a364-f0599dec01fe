package dropdown

import (
	"backend-common-lib/model"
	"content-service/internal/model/dto"
	auctionAssetRepo "content-service/internal/repository/auction_asset"
	auctionNameRepo "content-service/internal/repository/auction_name"
	buyerRepo "content-service/internal/repository/buyer"
	configParameterRepo "content-service/internal/repository/config_parameter"
	helpRequestReasonRepo "content-service/internal/repository/help_request_reason"
	assetGroupRepo "content-service/internal/repository/master_asset_group"
	floorRepo "content-service/internal/repository/master_asset_location_floor"
	assetTypeRepo "content-service/internal/repository/master_asset_type"
	auctionTypeRepo "content-service/internal/repository/master_auction_type"
	branchRepo "content-service/internal/repository/master_branch"
	cityRepo "content-service/internal/repository/master_city"
	customerGroupRepo "content-service/internal/repository/master_customer_group"
	customerTypeRepo "content-service/internal/repository/master_customer_type"
	departmentRepo "content-service/internal/repository/master_department"
	districtRepo "content-service/internal/repository/master_district"
	eventRepo "content-service/internal/repository/master_event"
	prefixNameRepo "content-service/internal/repository/master_prefix_name"
	regionRepo "content-service/internal/repository/master_region"
	saleChannelRepo "content-service/internal/repository/master_sale_channel"
	subDistrictRepo "content-service/internal/repository/master_sub_district"
	vatBusinessRepo "content-service/internal/repository/master_vat_business"
	vatCodeRepo "content-service/internal/repository/master_vat_code"
	vendorRepo "content-service/internal/repository/master_vendor"
	vendorGroupRepo "content-service/internal/repository/master_vendor_group"
	proxyBidCancelReasonRepo "content-service/internal/repository/proxy_bid_cancel_reason"
	reprintSlipReasonRepo "content-service/internal/repository/reprint_slip_reason"

	"github.com/gofiber/fiber/v2"
)

type DropdownService interface {
	GetAssetTypeDropdown(isActive string) ([]model.DropdownDto, error)
	GetAssetGroupDropdown(isActive string, assetTypeCode string) ([]model.DropdownDto, error)
	GetAssetGroupDropdownByMultipleAssetType(isActive string, req dto.AssetGroupMultiSelectDropdownReqDto) ([]dto.DropdownAssetGroupDto, error)
	GetBranchDropdown(isActive string) ([]model.DropdownDto, error)
	GetRegionDropdown(isActive string, countryCode string) ([]model.DropdownDto, error)
	GetEventDropdown(isActive string) ([]model.DropdownDto, error)
	GetVendorDropdown(isActive string) ([]model.DropdownDto, error)
	GetVendorGroupDropdown(isActive string) ([]model.DropdownDto, error)
	GetSaleChannelDropdown(isActive string) ([]model.DropdownDto, error)
	GetLotSettingDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetFloorStatusDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetConfigFeatureLotDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetSaleChannelConfigDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetAuctionStatusDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetPrefixNameDropdown(isActive string) ([]model.DropdownDto, error)
	GetVatBusinessDropdown(isActive string) ([]model.DropdownDto, error)
	GetDayBeforeDueDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetFloorDropdown(isActive string, branchId string) ([]model.DropdownDto, error)
	GetVatCodeDropdown(isActive string) ([]model.DropdownDto, error)
	GetDistrictDropdown(isActive string, cityCode string) ([]model.DropdownDto, error)
	GetCityDropdown(isActive string, regionCode string) ([]model.DropdownDto, error)
	GetNationalityDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetSubDistrictDropdown(isActive string, districtCode string) ([]model.DropdownDto, error)
	GetCustomerTypeDropdown(isActive string) ([]model.DropdownDto, error)
	GetCustomerGroupDropdown(isActive string) ([]model.DropdownDto, error)
	GetDepartmentDropdown(isActive string) ([]model.DropdownDto, error)
	GetAuctionNameDropdown(c *fiber.Ctx) ([]model.AuctionDropdownDto, error)
	GetBuyerDropdown(c *fiber.Ctx) ([]model.DropdownDto, error)
	GetProxyBidStatusDropdown(c *fiber.Ctx) ([]model.ConfigParameterDropdownDto, error)
	GetActionStatusDropdown(c *fiber.Ctx) ([]model.DropdownDto, error)
	GetProxyBidCancelReasonDropdown(isActive string) ([]model.DropdownDto, error)
	GetRoleDropdown(isActive string) ([]model.DropdownDto, error)
	GetConfigAuctionCollateralDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetConfigDisplayLocationDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetConfigCampaignEventTypeDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetConfigAuctionTypeDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetConfigServiceTypeDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetHelpRequestStatusDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetHelpRequestReasonDropdown(isActive string) ([]model.DropdownDto, error)
	GetReprintSlipReasonDropdown(isActive string) ([]model.DropdownDto, error)
	GetAuctionBidVoidReasonDropdown() ([]model.ConfigParameterDropdownDto, error)
	GetAnswerTypeDropdown() ([]model.DropdownDto, error)
	GetSurveyTypeDropdown() ([]model.DropdownDto, error)
	GetSurveyCriteriaDropdown(surveyType string) ([]model.DropdownDto, error)
}

type dropdownService struct {
	AssetTypeRepo            assetTypeRepo.MasterAssetTypeRepository
	AssetGroupRepo           assetGroupRepo.MasterAssetGroupRepository
	EventRepo                eventRepo.MasterEventRepository
	VendorRepo               vendorRepo.MasterVendorRepository
	VendorGroupRepo          vendorGroupRepo.MasterVendorGroupRepository
	SaleChannelRepo          saleChannelRepo.MasterSaleChannelRepository
	BranchRepo               branchRepo.MasterBranchRepository
	LotSettingRepo           configParameterRepo.ConfigParameterRepository
	FloorStatusRepo          configParameterRepo.ConfigParameterRepository
	ConfigFeatureLotRepo     configParameterRepo.ConfigParameterRepository
	SaleChannelConfigRepo    configParameterRepo.ConfigParameterRepository
	AuctionStatusRepo        configParameterRepo.ConfigParameterRepository
	PrefixNameRepo           prefixNameRepo.MasterPrefixNameRepository
	VatBusinessRepo          vatBusinessRepo.MasterVatBusinessRepository
	DayBeforeDueRepo         configParameterRepo.ConfigParameterRepository
	RegionRepo               regionRepo.MasterRegionRepository
	FloorRepo                floorRepo.MasterAssetLocationFloorRepository
	VatCodeRepo              vatCodeRepo.MasterVatCodeRepository
	DistrictRepo             districtRepo.MasterDistrictRepository
	CityRepo                 cityRepo.MasterCityRepository
	NationalityRepo          configParameterRepo.ConfigParameterRepository
	SubDistrictRepo          subDistrictRepo.MasterSubDistrictRepository
	CustomerTypeRepo         customerTypeRepo.MasterCustomerTypeRepository
	CustomerGroupRepo        customerGroupRepo.MasterCustomerGroupRepository
	DepartmentRepo           departmentRepo.MasterDepartmentRepository
	AuctionTypeRepo          auctionTypeRepo.MasterAuctionTypeRepository
	AuctionNameRepo          auctionNameRepo.AuctionNameRepository
	BuyerRepo                buyerRepo.BuyerRepository
	ConfigParameterRepo      configParameterRepo.ConfigParameterRepository
	ProxyBidCancelReasonRepo proxyBidCancelReasonRepo.ProxyBidCancelReasonRepository
	AuctionAssetRepo         auctionAssetRepo.AuctionAssetRepository
	HelpRequestReasonRepo    helpRequestReasonRepo.HelpRequestReasonRepository
	ReprintSlipReasonRepo    reprintSlipReasonRepo.ReprintSlipReasonRepository
}

func NewDropdownService(
	assetTypeRepo assetTypeRepo.MasterAssetTypeRepository,
	assetGroupRepo assetGroupRepo.MasterAssetGroupRepository,
	eventRepo eventRepo.MasterEventRepository,
	vendorRepo vendorRepo.MasterVendorRepository,
	vendorGroupRepo vendorGroupRepo.MasterVendorGroupRepository,
	saleChannelRepo saleChannelRepo.MasterSaleChannelRepository,
	branchRepo branchRepo.MasterBranchRepository,
	lotSettingRepo configParameterRepo.ConfigParameterRepository,
	floorStatusRepo configParameterRepo.ConfigParameterRepository,
	configFeatureLotRepo configParameterRepo.ConfigParameterRepository,
	saleChannelConfigRepo configParameterRepo.ConfigParameterRepository,
	auctionStatusRepo configParameterRepo.ConfigParameterRepository,
	prefixNameRepo prefixNameRepo.MasterPrefixNameRepository,
	vatBusinessRepo vatBusinessRepo.MasterVatBusinessRepository,
	dayBeforeDueRepo configParameterRepo.ConfigParameterRepository,
	regionRepo regionRepo.MasterRegionRepository,
	floorRepo floorRepo.MasterAssetLocationFloorRepository,
	vatCodeRepo vatCodeRepo.MasterVatCodeRepository,
	DistrictRepo districtRepo.MasterDistrictRepository,
	CityRepo cityRepo.MasterCityRepository,
	NationalityRepo configParameterRepo.ConfigParameterRepository,
	SubDistrictRepo subDistrictRepo.MasterSubDistrictRepository,
	CustomerTypeRepo customerTypeRepo.MasterCustomerTypeRepository,
	CustomerGroupRepo customerGroupRepo.MasterCustomerGroupRepository,
	DepartmentRepo departmentRepo.MasterDepartmentRepository,
	AuctionTypeRepo auctionTypeRepo.MasterAuctionTypeRepository,
	AuctionNameRepo auctionNameRepo.AuctionNameRepository,
	BuyerRepo buyerRepo.BuyerRepository,
	ConfigParameterRepo configParameterRepo.ConfigParameterRepository,
	ProxyBidCancelReasonRepo proxyBidCancelReasonRepo.ProxyBidCancelReasonRepository,
	AuctionAssetRepo auctionAssetRepo.AuctionAssetRepository,
	HelpRequestReasonRepo helpRequestReasonRepo.HelpRequestReasonRepository,
	ReprintSlipReasonRepo reprintSlipReasonRepo.ReprintSlipReasonRepository,

) DropdownService {
	return &dropdownService{
		AssetTypeRepo:            assetTypeRepo,
		AssetGroupRepo:           assetGroupRepo,
		EventRepo:                eventRepo,
		VendorRepo:               vendorRepo,
		VendorGroupRepo:          vendorGroupRepo,
		SaleChannelRepo:          saleChannelRepo,
		BranchRepo:               branchRepo,
		LotSettingRepo:           lotSettingRepo,
		FloorStatusRepo:          floorStatusRepo,
		ConfigFeatureLotRepo:     configFeatureLotRepo,
		SaleChannelConfigRepo:    saleChannelConfigRepo,
		AuctionStatusRepo:        auctionStatusRepo,
		PrefixNameRepo:           prefixNameRepo,
		VatBusinessRepo:          vatBusinessRepo,
		DayBeforeDueRepo:         dayBeforeDueRepo,
		RegionRepo:               regionRepo,
		FloorRepo:                floorRepo,
		VatCodeRepo:              vatCodeRepo,
		DistrictRepo:             DistrictRepo,
		CityRepo:                 CityRepo,
		NationalityRepo:          NationalityRepo,
		SubDistrictRepo:          SubDistrictRepo,
		CustomerTypeRepo:         CustomerTypeRepo,
		CustomerGroupRepo:        CustomerGroupRepo,
		DepartmentRepo:           DepartmentRepo,
		AuctionTypeRepo:          AuctionTypeRepo,
		AuctionNameRepo:          AuctionNameRepo,
		BuyerRepo:                BuyerRepo,
		ConfigParameterRepo:      ConfigParameterRepo,
		ProxyBidCancelReasonRepo: ProxyBidCancelReasonRepo,
		AuctionAssetRepo:         AuctionAssetRepo,
		HelpRequestReasonRepo:    HelpRequestReasonRepo,
		ReprintSlipReasonRepo:    ReprintSlipReasonRepo,
	}
}
