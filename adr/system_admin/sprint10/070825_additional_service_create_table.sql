CREATE TABLE public.additional_service (
    id serial4 NOT NULL,
    service_type int4 not NULL,
    service_name varchar(100) NOT NULL,
    detail varchar(2000) NULL,
    start_date date NOT NULL,
    end_date date NULL,
    is_active bool DEFAULT false NOT NULL,
    created_by int4 NOT NULL,
    updated_by int4 NULL,
    deleted_by int4 NULL,
    created_date timestamp DEFAULT now() NOT NULL,
    updated_date timestamp DEFAULT now() NULL,
    deleted_date timestamp NULL,
    CONSTRAINT additional_service_pkey PRIMARY KEY (id)
);

CREATE TABLE public.additional_service_asset (
    id serial4 NOT NULL,
    additional_service_id int4 not NULL,
    asset_type_id int4 not NULL,
    asset_group_id int4 NULL,
    deleted_date timestamp NULL,
    CONSTRAINT additional_service_asset_pkey PRIMARY KEY (id)
);

ALTER TABLE public.additional_service_asset
ADD CONSTRAINT additional_service_asset_additional_service_fk FOREIGN KEY (additional_service_id) REFERENCES public.additional_service (id);

CREATE TABLE public.additional_service_customer_group (
    id serial4 NOT NULL,
    additional_service_id int4 not NULL,
    customer_group_id int4 not NULL,
    deleted_date timestamp NULL,
    CONSTRAINT additional_service_customer_group_pkey PRIMARY KEY (id)
);

ALTER TABLE public.additional_service_customer_group
ADD CONSTRAINT additional_service_customer_group_additional_service_fk FOREIGN KEY (additional_service_id) REFERENCES public.additional_service_customer_group (id);

CREATE TABLE public.additional_service_provider (
    id serial4 NOT NULL,
    additional_service_id int4 not NULL,
    provider_name varchar(100) NOT NULL,
    provider_link varchar(100) NOT NULL,
    deleted_date timestamp NULL,
    CONSTRAINT additional_service_provider_pkey PRIMARY KEY (id)
);

ALTER TABLE public.additional_service_provider
ADD CONSTRAINT additional_service_provider_additional_service_fk FOREIGN KEY (additional_service_id) REFERENCES public.additional_service_provider (id);

CREATE TABLE public.additional_service_display (
    id serial4 NOT NULL,
    additional_service_id int4 not NULL,
    display_id int4 not NULL,
    deleted_date timestamp NULL,
    CONSTRAINT additional_service_display_pkey PRIMARY KEY (id)
);