-- Auto-generated permissions seed
BEGIN;
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-types', 'GET', 'content-service', 'master_asset_types', 'use for get master asset types', 'view', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-groups', 'GET', 'content-service', 'master_asset_groups', 'use for get master asset groups', 'view', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/events/all', 'GET', 'content-service', 'master_all', 'use for get master all', 'view', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/branches/all', 'GET', 'content-service', 'master_all', 'use for get master all', 'view', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-groups', 'GET', 'content-service', 'master_customer_groups', 'use for get master customer groups', 'view', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/payment-methods', 'POST', 'content-service', 'master_payment_methods', 'use for create master payment methods', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/payment-methods/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-types', 'POST', 'content-service', 'master_asset_types', 'use for create master asset types', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-types/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-groups', 'POST', 'content-service', 'master_asset_groups', 'use for create master asset groups', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-groups/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/events', 'POST', 'content-service', 'master_events', 'use for create master events', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/events/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sale-channels', 'POST', 'content-service', 'master_sale_channels', 'use for create master sale channels', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sale-channels/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/branches', 'POST', 'content-service', 'master_branches', 'use for create master branches', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/branches/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/banks', 'POST', 'content-service', 'master_banks', 'use for create master banks', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/banks/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendors', 'POST', 'content-service', 'master_vendors', 'use for create master vendors', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendors/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendor-groups', 'POST', 'content-service', 'master_vendor_groups', 'use for create master vendor groups', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendor-groups/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/holidays', 'POST', 'content-service', 'master_holidays', 'use for create master holidays', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/holidays/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/postcodes', 'POST', 'content-service', 'master_postcodes', 'use for create master postcodes', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/postcodes/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-location-floors', 'POST', 'content-service', 'master_asset_location_floors', 'use for create master asset location floors', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-location-floors/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-groups', 'POST', 'content-service', 'master_customer_groups', 'use for create master customer groups', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-groups/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-types', 'POST', 'content-service', 'master_customer_types', 'use for create master customer types', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-types/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cost-revenues', 'POST', 'content-service', 'master_cost_revenues', 'use for create master cost revenues', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cost-revenues/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/departments', 'POST', 'content-service', 'master_departments', 'use for create master departments', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/departments/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customers', 'POST', 'content-service', 'master_customers', 'use for create master customers', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customers/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/districts', 'POST', 'content-service', 'master_districts', 'use for create master districts', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/districts/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/countries', 'POST', 'content-service', 'master_countries', 'use for create master countries', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/countries/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/prefix-names', 'POST', 'content-service', 'master_prefix_names', 'use for create master prefix names', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/prefix-names/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-codes', 'POST', 'content-service', 'master_vat_codes', 'use for create master vat codes', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-codes/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/reasons', 'POST', 'content-service', 'master_reasons', 'use for create master reasons', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/reasons/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/regions', 'POST', 'content-service', 'master_regions', 'use for create master regions', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/regions/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-businesses', 'POST', 'content-service', 'master_vat_businesses', 'use for create master vat businesses', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-businesses/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sub-districts', 'POST', 'content-service', 'master_sub_districts', 'use for create master sub districts', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sub-districts/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cities', 'POST', 'content-service', 'master_cities', 'use for create master cities', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cities/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-types', 'POST', 'content-service', 'master_register_types', 'use for create master register types', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-types/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-type-cars', 'POST', 'content-service', 'master_register_type_cars', 'use for create master register type cars', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-type-cars/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-seller-offers', 'POST', 'content-service', 'master_customer_seller_offers', 'use for create master customer seller offers', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-seller-offers/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/seller-offers', 'POST', 'content-service', 'master_seller_offers', 'use for create master seller offers', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/seller-offers/sync', 'POST', 'content-service', 'master_sync', 'use for create master sync', 'create', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/payment-methods/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-types/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-groups/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/events/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sale-channels/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/branches/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/banks/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendors/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vendor-groups/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/holidays/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/postcodes/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/asset-location-floors/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-groups/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-types/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cost-revenues/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/departments/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customers/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/districts/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/countries/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/prefix-names/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-codes/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/reasons/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/regions/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/vat-businesses/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/sub-districts/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/cities/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-types/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/register-type-cars/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/customer-seller-offers/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
INSERT INTO users.permissions ("path", "method", service_name, feature_key, description, "action", is_auth) VALUES('/api/v1/content-service/master/seller-offers/status/:id', 'PUT', 'content-service', 'master_status', 'use for update master status', 'update', true);
COMMIT;
