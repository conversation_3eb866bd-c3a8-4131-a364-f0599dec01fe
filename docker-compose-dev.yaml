services:
  content-service:
    image: harbor-npd.auct.local/auctlive-dev/content-service:${IMAGE_TAG}
    build:
      context: .
      dockerfile: Dockerfile.dev
      args:
        ENV_FILE: .env.dev

    volumes:
      - ./cert:/app/cert
    environment:
      APP_ENV: dev
    networks:
      - auction-microservices_auctlive-net

networks:
  auction-microservices_auctlive-net:
    external: true
