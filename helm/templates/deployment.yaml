apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "content-service.fullname" . }}
  labels:
    app: {{ include "content-service.name" . }}
spec:
  revisionHistoryLimit: 5
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ include "content-service.name" . }}
  template:
    metadata:
      labels:
        app: {{ include "content-service.name" . }}
    spec:
      serviceAccountName: default
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- range .Values.imagePullSecrets }}
        - name: {{ .name }}
        {{- end }}
      {{- end }}
      containers:
        - name: content-service
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.containerPort }}
          env:
            - name: GIN_MODE
              value: "debug"
            - name: APP_ENV
              value: {{ .Values.env.ENVIRONMENT | default "dev" | quote }}
          {{- if and .Values.cert.enabled .Values.cert.mountPath .Values.cert.secretName }}
          
          volumeMounts:
            - name: erp-ca
              mountPath: {{ .Values.cert.mountPath }}
              readOnly: true
          {{- end }}
          resources:
{{- toYaml .Values.resources | nindent 12 }}
      {{- if and .Values.cert.enabled .Values.cert.secretName }}
      volumes:
        - name: erp-ca
          secret:
            secretName: {{ .Values.cert.secretName }}
            items:
              - key: erp_cert.crt
                path: erp_cert.crt
      {{- end }}
