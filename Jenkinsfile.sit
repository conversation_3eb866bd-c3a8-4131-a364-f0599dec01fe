pipeline {
  agent any

  environment {
    ACTION_BRANCH = 'sit'
    HARBOR_HOST = 'harbor-npd.auct.local'
    PROJECT_NAME = 'auctlive-sit'
    IMAGE_NAME = 'content-service'
    HARBOR_CREDENTIAL = 'harbor-jenkin'
    KUBE_CREDENTIAL = 'kubeconfig-jenkins'
    DEPLOY_NAMESPACE = 'auction-sit'
  }

  stages {
    stage('Checkout') {
      steps {
        checkout([$class: 'GitSCM',
          branches: [[name: "*/${ACTION_BRANCH}"]],
          userRemoteConfigs: [[
            url: 'https://<EMAIL>/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-content',
            credentialsId: 'azure-pat'
          ]]
        ])
        echo "Checked out branch: ${ACTION_BRANCH}"
      }
    }

    stage('Image Tag') {
      steps {
        script {
          def tag = sh(script: 'git rev-parse --short=7 HEAD', returnStdout: true).trim()
          env.IMAGE_TAG = tag
          echo "Set IMAGE_TAG=${env.IMAGE_TAG}"
        }
      }
    }

    stage('Clone Common Lib') {
      steps {
        dir('backend-common-lib') {
          git credentialsId: 'azure-pat',
            url: 'https://dev.azure.com/sirisoftgroup/AUCT-Revamp-Auctlive-Application/_git/auction-backend-common-lib',
            branch: 'main',
            changelog: false,
            poll: false
        }
      }
    }
    
    stage('Prepare Certs') {
      steps {
        // ดึง cert (.pem)
        withCredentials([
          file(credentialsId: 'secret-erp_cert', variable: 'ERP_CERT_PEM')
        ]) {
          sh """
              mkdir -p cert
              cp $ERP_CERT_PEM cert/erp_cert.crt
              ls -l cert
          """
        }
      }
    }

    stage('Build Image') {
      steps {
        withDockerRegistry(credentialsId: "${HARBOR_CREDENTIAL}", url: "https://${HARBOR_HOST}") {
          sh """
            export IMAGE_TAG=${IMAGE_TAG}
            docker compose -f docker-compose-${ACTION_BRANCH}.yaml build --pull ${IMAGE_NAME}
          """
        }
      }
    }
    
    stage('Push Image') {
      steps {
        withDockerRegistry(credentialsId: "${HARBOR_CREDENTIAL}", url: "https://${HARBOR_HOST}") {
          sh """
            export IMAGE_TAG=${IMAGE_TAG}
            docker compose -f docker-compose-${ACTION_BRANCH}.yaml push ${IMAGE_NAME}
          """
        }
      }
    }
    
     stage('Publish ERP') {
      steps {
        withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
          sh """
            set -e
            export KUBECONFIG=$KUBECONFIG
            test -f cert/erp_cert.crt || { echo 'Missing cert/erp_cert.crt'; exit 1; }
    
            kubectl -n $DEPLOY_NAMESPACE create secret generic erp-ca-cert \
              --from-file=erp_cert.crt=cert/erp_cert.crt \
              --dry-run=client -o yaml | kubectl apply -f -
    
            kubectl -n $DEPLOY_NAMESPACE get secret erp-ca-cert -o yaml >/dev/null
          """
        }
      }
    }
    stage('Deploy') {
      steps {
        withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
          sh """
            export KUBECONFIG=$KUBECONFIG
            helm upgrade --install ${IMAGE_NAME} ./helm \
              -f helm/values-${ACTION_BRANCH}.yaml \
              --namespace ${DEPLOY_NAMESPACE} --create-namespace \
              --set image.repository=${HARBOR_HOST}/${PROJECT_NAME}/${IMAGE_NAME} \
              --set image.tag=${IMAGE_TAG} 
          """
        }
      }
    }
    stage('Rollout') {
      steps {
        withCredentials([file(credentialsId: "${KUBE_CREDENTIAL}", variable: 'KUBECONFIG')]) {
          sh """
            set -e
            export KUBECONFIG=$KUBECONFIG
            kubectl -n $DEPLOY_NAMESPACE rollout restart deploy/$IMAGE_NAME
            kubectl -n $DEPLOY_NAMESPACE rollout status deploy/$IMAGE_NAME --timeout=2m
          """
        }
      }
    }
    stage('Clean') {
      steps {
        sh '''
            echo "Cleaning system..."
            df -h
            docker image prune -af --filter "until=24h"
            docker builder prune -af --filter "until=24h"
        '''
      }
    }
  }

  post {
    always {
      cleanWs()
    }
    success {
      echo "Deployed: ${IMAGE_NAME}:${IMAGE_TAG} to ${DEPLOY_NAMESPACE}"
    }
    failure {
      echo "Deploy failed"
    }
  }
}
